#!/usr/bin/env python3
"""
Diagnostic script to analyze why SQL-based forecasts are too low compared to historical data
"""

import pandas as pd
import numpy as np
import sys
import os
import pyodbc
import sqlalchemy
from sqlalchemy import create_engine
import warnings
warnings.filterwarnings('ignore')

def create_sql_server_connection(connection_string):
    """Create SQL Server connection using SQLAlchemy"""
    try:
        # Build SQLAlchemy connection string for Windows Authentication
        connection_url = "mssql+pyodbc://@*************/SysproCompanyPAR?driver=SQL+Server&trusted_connection=yes"
        engine = create_engine(connection_url)
        return engine
    except Exception as e:
        print(f"Error creating SQL Server connection: {str(e)}")
        return None

def load_data_from_sql_server(connection_string, sql_query):
    """Load data from SQL Server using the provided connection string and query"""
    try:
        engine = create_sql_server_connection(connection_string)
        if engine is None:
            return pd.DataFrame()

        # Execute query and load data
        print("Connecting to SQL Server and loading data...")
        df = pd.read_sql(sql_query, engine)

        # Close the connection
        engine.dispose()

        return df
    except Exception as e:
        print(f"Error loading data from SQL Server: {str(e)}")
        return pd.DataFrame()

def preprocess_data(df):
    """Basic data preprocessing"""
    if df.empty:
        return df

    # Convert Period to datetime
    if 'Period' in df.columns:
        df['Period'] = pd.to_datetime(df['Period'], format='%Y-%m', errors='coerce')

    # Ensure numeric columns
    numeric_cols = ['Volume', 'NSV']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

    # Remove rows with missing critical data
    df = df.dropna(subset=['Period', 'Volume', 'NSV'])

    # Remove zero or negative volumes
    df = df[df['Volume'] > 0]

    return df

def load_sql_data():
    """Load data from SQL Server"""
    connection_string = "DRIVER=SQL Server;SERVER=*************;UID=syspro_report;Trusted_Connection=Yes;APP=2007 Microsoft Office system;WSID=PFLPOM0083;DATABASE=SysproCompanyPAR"
    sql_file_path = os.path.join("config", "sales.sql")
    
    if os.path.exists(sql_file_path):
        with open(sql_file_path, 'r', encoding='utf-8') as file:
            sql_query = file.read()
        
        df = load_data_from_sql_server(connection_string, sql_query)
        if not df.empty:
            df = preprocess_data(df)
        return df
    return pd.DataFrame()

def load_csv_data():
    """Load data from local CSV"""
    local_file_path = os.path.join("data", "sales.csv")
    if os.path.exists(local_file_path):
        df = pd.read_csv(local_file_path)
        df = preprocess_data(df)
        return df
    return pd.DataFrame()

def compare_data_sources():
    """Compare SQL vs CSV data characteristics"""
    print("Loading and Comparing Data Sources")
    print("=" * 60)
    
    # Load both data sources
    sql_data = load_sql_data()
    csv_data = load_csv_data()
    
    print(f"SQL Data: {len(sql_data)} records")
    print(f"CSV Data: {len(csv_data)} records")
    
    if sql_data.empty:
        print("❌ No SQL data loaded")
        return None, None
    
    if csv_data.empty:
        print("❌ No CSV data loaded")
        return sql_data, None
    
    # Compare basic statistics
    print(f"\nData Range Comparison:")
    print(f"SQL Date Range: {sql_data['Period'].min()} to {sql_data['Period'].max()}")
    print(f"CSV Date Range: {csv_data['Period'].min()} to {csv_data['Period'].max()}")
    
    print(f"\nVolume Statistics:")
    print(f"SQL Volume - Mean: {sql_data['Volume'].mean():.0f}, Median: {sql_data['Volume'].median():.0f}")
    print(f"CSV Volume - Mean: {csv_data['Volume'].mean():.0f}, Median: {csv_data['Volume'].median():.0f}")
    
    print(f"\nNSV Statistics:")
    print(f"SQL NSV - Mean: {sql_data['NSV'].mean():.0f}, Median: {sql_data['NSV'].median():.0f}")
    print(f"CSV NSV - Mean: {csv_data['NSV'].mean():.0f}, Median: {csv_data['NSV'].median():.0f}")
    
    # Compare product-customer combinations
    sql_combos = sql_data.groupby(['Product', 'Customer']).size().reset_index(name='Count')
    csv_combos = csv_data.groupby(['Product', 'Customer']).size().reset_index(name='Count')
    
    print(f"\nProduct-Customer Combinations:")
    print(f"SQL: {len(sql_combos)} unique combinations")
    print(f"CSV: {len(csv_combos)} unique combinations")
    
    # Find common combinations for detailed comparison
    sql_combo_keys = set(zip(sql_combos['Product'], sql_combos['Customer']))
    csv_combo_keys = set(zip(csv_combos['Product'], csv_combos['Customer']))
    common_combos = sql_combo_keys.intersection(csv_combo_keys)
    
    print(f"Common combinations: {len(common_combos)}")
    
    return sql_data, csv_data

def analyze_data_patterns(data, data_source_name):
    """Analyze data patterns for a specific data source"""
    print(f"\nAnalyzing Data Patterns for {data_source_name}")
    print("=" * 60)

    if data.empty:
        print("❌ No data to analyze")
        return []

    # Get top 5 product-customer combinations by volume
    combo_volumes = data.groupby(['Product', 'Customer'])['Volume'].sum().sort_values(ascending=False).head(5)

    print(f"Top 5 Product-Customer Combinations by Volume:")
    for (product, customer), total_volume in combo_volumes.items():
        print(f"  {product} - {customer}: {total_volume:.0f}")

    # Analyze each combination
    analysis_results = []

    for (product, customer), total_volume in combo_volumes.items():
        combo_data = data[(data['Product'] == product) & (data['Customer'] == customer)].copy()
        combo_data = combo_data.sort_values('Period')

        if len(combo_data) < 6:
            print(f"\n⚠️ Skipping {product}-{customer}: Only {len(combo_data)} months of data")
            continue

        print(f"\n📊 Analyzing {product} - {customer}")
        print(f"   Data points: {len(combo_data)}")
        print(f"   Date range: {combo_data['Period'].min()} to {combo_data['Period'].max()}")
        print(f"   Volume range: {combo_data['Volume'].min():.0f} - {combo_data['Volume'].max():.0f}")
        print(f"   Average volume: {combo_data['Volume'].mean():.0f}")
        print(f"   Median volume: {combo_data['Volume'].median():.0f}")
        print(f"   Volume std dev: {combo_data['Volume'].std():.0f}")

        # Calculate basic growth trends
        if len(combo_data) >= 12:
            recent_6_months = combo_data.tail(6)['Volume'].mean()
            previous_6_months = combo_data.iloc[-12:-6]['Volume'].mean() if len(combo_data) >= 12 else combo_data.head(6)['Volume'].mean()
            growth_trend = (recent_6_months - previous_6_months) / previous_6_months if previous_6_months > 0 else 0
            print(f"   Recent 6-month trend: {growth_trend:.1%}")
        else:
            growth_trend = 0
            print(f"   Insufficient data for trend analysis")

        analysis_results.append({
            'Product': product,
            'Customer': customer,
            'DataSource': data_source_name,
            'DataPoints': len(combo_data),
            'AvgVolume': combo_data['Volume'].mean(),
            'MedianVolume': combo_data['Volume'].median(),
            'StdVolume': combo_data['Volume'].std(),
            'GrowthTrend': growth_trend,
            'TotalVolume': total_volume
        })

    return analysis_results

def diagnose_low_forecasts():
    """Main diagnostic function"""
    print("SQL Data Diagnostic Analysis")
    print("=" * 60)

    # Load and compare data sources
    sql_data, csv_data = compare_data_sources()

    if sql_data is None or sql_data.empty:
        print("❌ Cannot proceed without SQL data")
        return

    # Analyze data patterns for SQL data
    sql_results = analyze_data_patterns(sql_data, "SQL Server")

    # Analyze data patterns for CSV data if available
    csv_results = []
    if csv_data is not None and not csv_data.empty:
        csv_results = analyze_data_patterns(csv_data, "CSV File")

    # Compare results if both are available
    if sql_results and csv_results:
        print(f"\n" + "=" * 60)
        print("COMPARISON ANALYSIS")
        print("=" * 60)

        sql_df = pd.DataFrame(sql_results)
        csv_df = pd.DataFrame(csv_results)

        print(f"SQL Data Characteristics:")
        print(f"  Average Volume: {sql_df['AvgVolume'].mean():.0f}")
        print(f"  Median Volume: {sql_df['MedianVolume'].mean():.0f}")
        print(f"  Average Growth Trend: {sql_df['GrowthTrend'].mean():.1%}")

        print(f"\nCSV Data Characteristics:")
        print(f"  Average Volume: {csv_df['AvgVolume'].mean():.0f}")
        print(f"  Median Volume: {csv_df['MedianVolume'].mean():.0f}")
        print(f"  Average Growth Trend: {csv_df['GrowthTrend'].mean():.1%}")

        # Compare specific combinations
        print(f"\nDirect Comparison (same Product-Customer combinations):")
        for sql_row in sql_results:
            matching_csv = next((csv_row for csv_row in csv_results
                               if csv_row['Product'] == sql_row['Product']
                               and csv_row['Customer'] == sql_row['Customer']), None)
            if matching_csv:
                sql_avg = sql_row['AvgVolume']
                csv_avg = matching_csv['AvgVolume']
                ratio = sql_avg / csv_avg if csv_avg > 0 else 0
                print(f"  {sql_row['Product']}-{sql_row['Customer']}: SQL={sql_avg:.0f}, CSV={csv_avg:.0f}, Ratio={ratio:.2f}")

    # Identify potential issues
    print(f"\n" + "=" * 60)
    print("POTENTIAL ISSUES IDENTIFIED")
    print("=" * 60)

    if sql_results:
        sql_df = pd.DataFrame(sql_results)

        # Check for declining trends
        declining_trends = sql_df[sql_df['GrowthTrend'] < -0.2]  # More than 20% decline
        if len(declining_trends) > 0:
            print(f"⚠️ {len(declining_trends)} combinations show declining trends (>20%)")
            for _, row in declining_trends.iterrows():
                print(f"  {row['Product']}-{row['Customer']}: {row['GrowthTrend']:.1%} trend")

        # Check for high volatility
        high_volatility = sql_df[sql_df['StdVolume'] / sql_df['AvgVolume'] > 0.5]  # CV > 50%
        if len(high_volatility) > 0:
            print(f"⚠️ {len(high_volatility)} combinations have high volatility (CV > 50%)")

        # Check data sufficiency
        insufficient_data = sql_df[sql_df['DataPoints'] < 18]  # Less than 18 months
        if len(insufficient_data) > 0:
            print(f"⚠️ {len(insufficient_data)} combinations have insufficient data (<18 months)")

    print(f"\n" + "=" * 60)
    print("RECOMMENDATIONS")
    print("=" * 60)

    print("1. Check if SQL data aggregation matches CSV data structure")
    print("2. Verify date range coverage in SQL query")
    print("3. Review product filtering in SQL query (StockCode LIKE 'F%')")
    print("4. Check for data quality issues in source system")
    print("5. Consider adjusting forecast validation rules for SQL data")

    return sql_results, csv_results

if __name__ == "__main__":
    diagnose_low_forecasts()
