#!/usr/bin/env python3
"""
Test script for SQL Server connectivity
"""

import pyodbc
import sqlalchemy
from sqlalchemy import create_engine
import pandas as pd
import os

def test_pyodbc_connection():
    """Test direct pyodbc connection"""
    connection_string = "DRIVER=SQL Server;SERVER=*************;UID=syspro_report;Trusted_Connection=Yes;APP=2007 Microsoft Office system;WSID=PFLPOM0083;DATABASE=SysproCompanyPAR"
    
    try:
        print("Testing pyodbc connection...")
        conn = pyodbc.connect(connection_string, timeout=30)
        cursor = conn.cursor()
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        print(f"✅ pyodbc connection successful: {result}")
        conn.close()
        return True
    except Exception as e:
        print(f"❌ pyodbc connection failed: {str(e)}")
        return False

def test_sqlalchemy_connection():
    """Test SQLAlchemy connection"""
    try:
        print("Testing SQLAlchemy connection...")
        # Build SQLAlchemy connection string for Windows Authentication
        connection_url = "mssql+pyodbc://@*************/SysproCompanyPAR?driver=SQL+Server&trusted_connection=yes"
        
        engine = create_engine(connection_url, connect_args={"timeout": 30})
        
        with engine.connect() as conn:
            result = conn.execute(sqlalchemy.text("SELECT 1 as test"))
            row = result.fetchone()
            print(f"✅ SQLAlchemy connection successful: {row}")
        
        engine.dispose()
        return True
    except Exception as e:
        print(f"❌ SQLAlchemy connection failed: {str(e)}")
        return False

def test_sql_query():
    """Test the actual sales query"""
    sql_file_path = os.path.join("config", "sales.sql")
    
    if not os.path.exists(sql_file_path):
        print(f"❌ SQL file not found: {sql_file_path}")
        return False
    
    try:
        # Load SQL query
        with open(sql_file_path, 'r', encoding='utf-8') as file:
            sql_query = file.read()
        
        print("Testing sales query execution...")
        print(f"Query length: {len(sql_query)} characters")
        
        # Test with SQLAlchemy
        connection_url = "mssql+pyodbc://@*************/SysproCompanyPAR?driver=SQL+Server&trusted_connection=yes"
        engine = create_engine(connection_url, connect_args={"timeout": 30})
        
        # Execute query with limit for testing (use TOP for older SQL Server)
        test_query = sql_query.replace("SELECT ", "SELECT TOP 10 ", 1)
        
        df = pd.read_sql(test_query, engine)
        engine.dispose()
        
        print(f"✅ Query executed successfully!")
        print(f"   Rows returned: {len(df)}")
        print(f"   Columns: {list(df.columns)}")
        
        if len(df) > 0:
            print(f"   Sample data:")
            print(df.head(3).to_string())
        
        return True
    except Exception as e:
        print(f"❌ Query execution failed: {str(e)}")
        return False

def check_sql_drivers():
    """Check available SQL Server drivers"""
    try:
        drivers = pyodbc.drivers()
        print("Available ODBC drivers:")
        sql_drivers = [d for d in drivers if 'SQL' in d.upper()]
        for driver in sql_drivers:
            print(f"  - {driver}")
        
        if not sql_drivers:
            print("❌ No SQL Server drivers found!")
            return False
        return True
    except Exception as e:
        print(f"❌ Error checking drivers: {str(e)}")
        return False

if __name__ == "__main__":
    print("SQL Server Connection Test")
    print("=" * 50)
    
    # Check drivers
    print("\n1. Checking ODBC drivers...")
    check_sql_drivers()
    
    # Test pyodbc
    print("\n2. Testing pyodbc connection...")
    pyodbc_success = test_pyodbc_connection()
    
    # Test SQLAlchemy
    print("\n3. Testing SQLAlchemy connection...")
    sqlalchemy_success = test_sqlalchemy_connection()
    
    # Test actual query
    if pyodbc_success or sqlalchemy_success:
        print("\n4. Testing sales query...")
        test_sql_query()
    else:
        print("\n4. Skipping query test due to connection failures")
    
    print("\n" + "=" * 50)
    print("Test completed!")
