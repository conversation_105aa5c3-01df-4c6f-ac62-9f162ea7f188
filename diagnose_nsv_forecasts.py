#!/usr/bin/env python3
"""
Diagnose NSV forecast accuracy - check why forecasts are 51M vs historical 31M
"""

import pandas as pd
import numpy as np
import sys
import os
import warnings
warnings.filterwarnings('ignore')

def create_sql_server_connection(connection_string):
    """Create SQL Server connection using SQLAlchemy"""
    try:
        from sqlalchemy import create_engine
        connection_url = "mssql+pyodbc://@10.101.102.50/SysproCompanyPAR?driver=SQL+Server&trusted_connection=yes"
        engine = create_engine(connection_url)
        return engine
    except Exception as e:
        print(f"Error creating SQL Server connection: {str(e)}")
        return None

def preprocess_data(df):
    """Basic data preprocessing"""
    if df.empty:
        return df
    
    # Convert Period to datetime
    if 'Period' in df.columns:
        df['Period'] = pd.to_datetime(df['Period'], format='%Y-%m', errors='coerce')
    
    # Ensure numeric columns
    numeric_cols = ['Volume', 'NSV']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Remove rows with missing critical data
    df = df.dropna(subset=['Period', 'Volume', 'NSV'])
    
    # Remove zero or negative volumes
    df = df[df['Volume'] > 0]
    
    return df

def analyze_nsv_patterns():
    """Analyze NSV patterns to understand forecast discrepancy"""
    
    print("NSV Forecast Analysis")
    print("=" * 60)
    
    # Load SQL data
    connection_string = "DRIVER=SQL Server;SERVER=10.101.102.50;UID=syspro_report;Trusted_Connection=Yes;APP=2007 Microsoft Office system;WSID=PFLPOM0083;DATABASE=SysproCompanyPAR"
    sql_file_path = os.path.join("config", "sales.sql")
    
    if not os.path.exists(sql_file_path):
        print(f"❌ SQL file not found: {sql_file_path}")
        return
    
    with open(sql_file_path, 'r', encoding='utf-8') as file:
        sql_query = file.read()
    
    engine = create_sql_server_connection(connection_string)
    if engine is None:
        print("❌ Failed to create database connection")
        return
    
    try:
        # Load and preprocess data
        print("📊 Loading data...")
        df = pd.read_sql(sql_query, engine)
        engine.dispose()
        
        if df.empty:
            print("❌ No data loaded")
            return
        
        df = preprocess_data(df)
        print(f"✅ Data loaded: {len(df)} records")
        
        # Calculate monthly aggregates
        monthly_data = df.groupby('Period').agg({
            'Volume': 'sum',
            'NSV': 'sum'
        }).reset_index()
        
        # Overall statistics
        print(f"\n📈 Historical NSV Analysis:")
        total_months = len(monthly_data)
        avg_monthly_nsv = monthly_data['NSV'].mean()
        avg_monthly_volume = monthly_data['Volume'].mean()
        avg_nsv_per_unit = avg_monthly_nsv / avg_monthly_volume if avg_monthly_volume > 0 else 0
        
        print(f"Total months: {total_months}")
        print(f"Average monthly NSV: {avg_monthly_nsv:,.0f} ({avg_monthly_nsv/1000000:.1f}M)")
        print(f"Average monthly Volume: {avg_monthly_volume:,.0f}")
        print(f"Average NSV per unit: {avg_nsv_per_unit:.2f}")
        
        # Recent trends (last 6 months)
        recent_data = monthly_data.tail(6)
        recent_avg_nsv = recent_data['NSV'].mean()
        recent_avg_volume = recent_data['Volume'].mean()
        recent_nsv_per_unit = recent_avg_nsv / recent_avg_volume if recent_avg_volume > 0 else 0
        
        print(f"\n📊 Recent 6-Month Trends:")
        print(f"Recent average monthly NSV: {recent_avg_nsv:,.0f} ({recent_avg_nsv/1000000:.1f}M)")
        print(f"Recent average monthly Volume: {recent_avg_volume:,.0f}")
        print(f"Recent NSV per unit: {recent_nsv_per_unit:.2f}")
        
        # Growth analysis
        if len(monthly_data) >= 12:
            recent_6 = monthly_data.tail(6)['NSV'].mean()
            previous_6 = monthly_data.iloc[-12:-6]['NSV'].mean()
            nsv_growth = (recent_6 - previous_6) / previous_6 if previous_6 > 0 else 0
            
            recent_6_vol = monthly_data.tail(6)['Volume'].mean()
            previous_6_vol = monthly_data.iloc[-12:-6]['Volume'].mean()
            volume_growth = (recent_6_vol - previous_6_vol) / previous_6_vol if previous_6_vol > 0 else 0
            
            print(f"\n📈 Growth Analysis (Recent 6 vs Previous 6 months):")
            print(f"NSV growth: {nsv_growth:.1%}")
            print(f"Volume growth: {volume_growth:.1%}")
            print(f"Price growth: {(nsv_growth - volume_growth):.1%}")
        
        # Analyze NSV per unit trends
        monthly_data['NSV_per_unit'] = monthly_data['NSV'] / monthly_data['Volume']
        nsv_per_unit_trend = monthly_data['NSV_per_unit'].pct_change().mean()
        
        print(f"\n💰 NSV per Unit Analysis:")
        print(f"Min NSV per unit: {monthly_data['NSV_per_unit'].min():.2f}")
        print(f"Max NSV per unit: {monthly_data['NSV_per_unit'].max():.2f}")
        print(f"Std deviation: {monthly_data['NSV_per_unit'].std():.2f}")
        print(f"Monthly trend: {nsv_per_unit_trend:.1%}")
        
        # Simulate current forecast logic
        print(f"\n" + "=" * 60)
        print("FORECAST SIMULATION")
        print("=" * 60)
        
        # Test different forecast scenarios
        scenarios = [
            ("Conservative", recent_avg_volume * 0.95, "95% of recent volume"),
            ("Realistic", recent_avg_volume, "Recent volume average"),
            ("Growth", recent_avg_volume * 1.1, "10% volume growth"),
            ("High Growth", recent_avg_volume * 1.3, "30% volume growth")
        ]
        
        print(f"Testing forecast scenarios with current NSV calculation logic:")
        print(f"(Using recent NSV per unit: {recent_nsv_per_unit:.2f} + 2% price increase)")
        
        for scenario_name, forecast_volume, description in scenarios:
            # Current logic: Volume * NSV_per_unit * 1.02
            forecast_nsv = forecast_volume * recent_nsv_per_unit * 1.02
            
            print(f"\n{scenario_name} Scenario ({description}):")
            print(f"  Forecast Volume: {forecast_volume:,.0f}")
            print(f"  Forecast NSV: {forecast_nsv:,.0f} ({forecast_nsv/1000000:.1f}M)")
            print(f"  vs Historical Avg: {forecast_nsv/avg_monthly_nsv:.1%}")
            print(f"  vs Recent Avg: {forecast_nsv/recent_avg_nsv:.1%}")
        
        # Identify the issue
        print(f"\n" + "=" * 60)
        print("ISSUE ANALYSIS")
        print("=" * 60)
        
        # Check if 51M forecast is realistic
        target_forecast_nsv = 51000000  # 51M
        required_volume = target_forecast_nsv / (recent_nsv_per_unit * 1.02)
        volume_increase_needed = (required_volume / recent_avg_volume - 1) * 100
        
        print(f"To achieve 51M monthly NSV forecast:")
        print(f"Required volume: {required_volume:,.0f}")
        print(f"Recent avg volume: {recent_avg_volume:,.0f}")
        print(f"Volume increase needed: {volume_increase_needed:.1f}%")
        
        if volume_increase_needed > 50:
            print(f"⚠️ WARNING: {volume_increase_needed:.1f}% volume increase is unrealistic!")
        
        # Recommendations
        print(f"\n" + "=" * 60)
        print("RECOMMENDATIONS")
        print("=" * 60)
        
        print(f"1. Historical monthly NSV average: {avg_monthly_nsv/1000000:.1f}M")
        print(f"2. Recent monthly NSV average: {recent_avg_nsv/1000000:.1f}M")
        print(f"3. Realistic forecast range: {recent_avg_nsv*0.95/1000000:.1f}M - {recent_avg_nsv*1.15/1000000:.1f}M")
        
        print(f"\nSuggested fixes:")
        print(f"1. Cap volume growth to realistic levels (max 20-30%)")
        print(f"2. Reduce price increase from 2% to 0-1%")
        print(f"3. Add NSV validation against historical patterns")
        print(f"4. Use recent NSV trends instead of just volume trends")
        
        return monthly_data
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        if engine:
            engine.dispose()
        return None

if __name__ == "__main__":
    analyze_nsv_patterns()
