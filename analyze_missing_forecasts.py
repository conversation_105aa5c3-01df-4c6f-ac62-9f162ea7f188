#!/usr/bin/env python3
"""
Analyze why some product-customer combinations are missing from forecasts
"""

import pandas as pd
import numpy as np
import sys
import os
import warnings
warnings.filterwarnings('ignore')

def create_sql_server_connection(connection_string):
    """Create SQL Server connection using SQLAlchemy"""
    try:
        from sqlalchemy import create_engine
        connection_url = "mssql+pyodbc://@10.101.102.50/SysproCompanyPAR?driver=SQL+Server&trusted_connection=yes"
        engine = create_engine(connection_url)
        return engine
    except Exception as e:
        print(f"Error creating SQL Server connection: {str(e)}")
        return None

def load_sql_query(file_path):
    """Load SQL query from file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except Exception as e:
        print(f"Error loading SQL file: {str(e)}")
        return None

def preprocess_data(df):
    """Basic data preprocessing (same as in app.py)"""
    if df.empty:
        return df
    
    # Convert Period to datetime
    if 'Period' in df.columns:
        df['Period'] = pd.to_datetime(df['Period'], format='%Y-%m', errors='coerce')
    
    # Ensure numeric columns
    numeric_cols = ['Volume', 'NSV']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Remove rows with missing critical data
    df = df.dropna(subset=['Period', 'Volume', 'NSV'])
    
    # Remove zero or negative volumes
    df = df[df['Volume'] > 0]
    
    return df

def analyze_forecast_coverage():
    """Analyze forecast coverage and identify missing combinations"""
    
    print("Forecast Coverage Analysis")
    print("=" * 60)
    
    # Load SQL data
    connection_string = "DRIVER=SQL Server;SERVER=10.101.102.50;UID=syspro_report;Trusted_Connection=Yes;APP=2007 Microsoft Office system;WSID=PFLPOM0083;DATABASE=SysproCompanyPAR"
    sql_file_path = os.path.join("config", "sales.sql")
    
    if not os.path.exists(sql_file_path):
        print(f"❌ SQL file not found: {sql_file_path}")
        return
    
    sql_query = load_sql_query(sql_file_path)
    if not sql_query:
        print("❌ Failed to load SQL query")
        return
    
    engine = create_sql_server_connection(connection_string)
    if engine is None:
        print("❌ Failed to create database connection")
        return
    
    try:
        # Load and preprocess data
        print("📊 Loading data...")
        df = pd.read_sql(sql_query, engine)
        engine.dispose()
        
        if df.empty:
            print("❌ No data loaded")
            return
        
        df = preprocess_data(df)
        print(f"✅ Data loaded and preprocessed: {len(df)} records")
        
        # Analyze all product-customer combinations
        print(f"\n📈 Combination Analysis:")
        combi_counts = df.groupby(['Product', 'Customer']).size().reset_index(name='DataPoints')
        
        print(f"Total unique combinations: {len(combi_counts)}")
        print(f"Data points distribution:")
        print(f"  Min: {combi_counts['DataPoints'].min()}")
        print(f"  Max: {combi_counts['DataPoints'].max()}")
        print(f"  Mean: {combi_counts['DataPoints'].mean():.1f}")
        print(f"  Median: {combi_counts['DataPoints'].median():.1f}")
        
        # Test different minimum data requirements
        print(f"\n🔍 Impact of Different Minimum Data Requirements:")
        
        min_requirements = [6, 12, 18, 24, 30, 36]
        
        for min_req in min_requirements:
            valid_combi = combi_counts[combi_counts['DataPoints'] >= min_req]
            excluded_combi = combi_counts[combi_counts['DataPoints'] < min_req]
            
            print(f"\nMinimum {min_req} months:")
            print(f"  ✅ Valid combinations: {len(valid_combi)} ({len(valid_combi)/len(combi_counts)*100:.1f}%)")
            print(f"  ❌ Excluded combinations: {len(excluded_combi)} ({len(excluded_combi)/len(combi_counts)*100:.1f}%)")
            
            if len(excluded_combi) > 0:
                # Show volume impact of excluded combinations
                excluded_products = excluded_combi[['Product', 'Customer']].values.tolist()
                excluded_data = df[df.apply(lambda row: [row['Product'], row['Customer']] in excluded_products, axis=1)]
                
                if not excluded_data.empty:
                    excluded_volume = excluded_data['Volume'].sum()
                    excluded_nsv = excluded_data['NSV'].sum()
                    total_volume = df['Volume'].sum()
                    total_nsv = df['NSV'].sum()
                    
                    print(f"  📊 Excluded volume: {excluded_volume:,.0f} ({excluded_volume/total_volume*100:.1f}% of total)")
                    print(f"  💰 Excluded NSV: {excluded_nsv:,.0f} ({excluded_nsv/total_nsv*100:.1f}% of total)")
        
        # Analyze excluded combinations in detail for current default (18 months)
        print(f"\n" + "=" * 60)
        print("DETAILED ANALYSIS - 18 MONTH REQUIREMENT (CURRENT DEFAULT)")
        print("=" * 60)
        
        min_data_points = 18
        valid_combi = combi_counts[combi_counts['DataPoints'] >= min_data_points]
        excluded_combi = combi_counts[combi_counts['DataPoints'] < min_data_points]
        
        print(f"Valid combinations: {len(valid_combi)}")
        print(f"Excluded combinations: {len(excluded_combi)}")
        
        if len(excluded_combi) > 0:
            print(f"\nTop 20 Excluded Combinations (by data points):")
            excluded_sorted = excluded_combi.sort_values('DataPoints', ascending=False).head(20)
            
            for _, row in excluded_sorted.iterrows():
                product = row['Product']
                customer = row['Customer']
                data_points = row['DataPoints']
                
                # Get volume and NSV for this combination
                combo_data = df[(df['Product'] == product) & (df['Customer'] == customer)]
                total_volume = combo_data['Volume'].sum()
                total_nsv = combo_data['NSV'].sum()
                
                print(f"  {product} - {customer}: {data_points} months, Volume={total_volume:,.0f}, NSV={total_nsv:,.0f}")
        
        # Analyze by volume significance
        print(f"\n📊 Analysis by Volume Significance:")
        
        # Calculate total volume for each combination
        combi_volume = df.groupby(['Product', 'Customer']).agg({
            'Volume': 'sum',
            'NSV': 'sum'
        }).reset_index()
        
        # Merge with data points
        combi_analysis = combi_counts.merge(combi_volume, on=['Product', 'Customer'])
        
        # Sort by volume
        combi_analysis = combi_analysis.sort_values('Volume', ascending=False)
        
        # Check high-volume combinations that are excluded
        excluded_analysis = combi_analysis[combi_analysis['DataPoints'] < min_data_points]
        
        if not excluded_analysis.empty:
            print(f"\nHigh-Volume Excluded Combinations (Top 10):")
            top_excluded = excluded_analysis.head(10)
            
            for _, row in top_excluded.iterrows():
                print(f"  {row['Product']} - {row['Customer']}: {row['DataPoints']} months")
                print(f"    Volume: {row['Volume']:,.0f}, NSV: {row['NSV']:,.0f}")
        
        # Recommendations
        print(f"\n" + "=" * 60)
        print("RECOMMENDATIONS")
        print("=" * 60)
        
        # Find optimal minimum that balances coverage and quality
        coverage_analysis = []
        for min_req in range(6, 25):
            valid = len(combi_counts[combi_counts['DataPoints'] >= min_req])
            coverage_pct = valid / len(combi_counts) * 100
            coverage_analysis.append((min_req, valid, coverage_pct))
        
        print(f"Coverage vs Quality Trade-off:")
        for min_req, valid, coverage_pct in coverage_analysis:
            if min_req in [6, 12, 15, 18, 21, 24]:
                print(f"  {min_req} months: {valid} combinations ({coverage_pct:.1f}% coverage)")
        
        # Find sweet spot (>80% coverage)
        good_coverage = [x for x in coverage_analysis if x[2] >= 80]
        if good_coverage:
            recommended = good_coverage[-1]  # Highest quality with >80% coverage
            print(f"\n💡 Recommended minimum: {recommended[0]} months")
            print(f"   Provides {recommended[2]:.1f}% coverage ({recommended[1]} combinations)")
        
        print(f"\nOptions to increase forecast coverage:")
        print(f"1. Reduce minimum data requirement from 18 to 12-15 months")
        print(f"2. Use different minimum requirements for different volume tiers")
        print(f"3. Implement fallback forecasting for low-data combinations")
        print(f"4. Use category-level or aggregate forecasting for excluded combinations")
        
        return combi_analysis
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        if engine:
            engine.dispose()
        return None

if __name__ == "__main__":
    analyze_forecast_coverage()
