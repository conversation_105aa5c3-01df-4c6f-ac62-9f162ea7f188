# SQL Server Integration for Sales Forecasting App

## Overview
The Sales Forecasting App now supports direct connection to SQL Server databases, allowing you to load real-time data from your Syspro ERP system.

## Features Added
- ✅ Direct SQL Server connectivity using Windows Authentication
- ✅ Custom SQL query support via `config/sales.sql`
- ✅ Connection testing functionality
- ✅ Data preview and validation
- ✅ Automatic data preprocessing
- ✅ Error handling and user feedback

## Configuration

### Connection String
The default connection string is configured for your Syspro database:
```
DRIVER=SQL Server;SERVER=10.101.102.50;UID=syspro_report;Trusted_Connection=Yes;APP=2007 Microsoft Office system;WSID=PFLPOM0083;DATABASE=SysproCompanyPAR
```

### SQL Query File
The SQL query is stored in `config/sales.sql` and includes:
- **Data Source**: ArSalesMove (main sales transactions)
- **Joins**: Customer, Product, Warehouse, and Category information
- **Time Range**: Last 24 months (excluding current month)
- **Filters**: Products starting with 'F', positive volumes and values
- **Aggregation**: Monthly summaries by Product-Customer combination

### Required Columns
The query returns these columns that match the app's requirements:
- `Customer` - Customer code
- `Name` - Customer name
- `Region` - Geographic region (Southern, Momase, Exports, etc.)
- `Warehouse` - Warehouse code
- `WHDescription` - Warehouse description
- `Product` - Product/Stock code
- `Description` - Product description
- `Category` - Product category
- `SubCategory` - Product subcategory
- `Period` - Month in YYYY-MM format
- `Volume` - Sales quantity
- `NSV` - Net Sales Value
- `GSV` - Gross Sales Value

## How to Use

### 1. Access SQL Server Option
1. Open the Sales Forecasting App
2. In the sidebar, select "Connect to SQL Server" from the data source options

### 2. Test Connection
1. Review the connection string (modify if needed)
2. Click "🔍 Test Connection" to verify connectivity
3. Wait for confirmation message

### 3. Load Data
1. Click "📊 Load Data from SQL Server"
2. The app will execute the SQL query and load data
3. Review the data preview to confirm successful loading
4. Data will be automatically preprocessed and validated

### 4. Generate Forecasts
1. Once data is loaded, configure forecast settings
2. Use the enhanced forecasting models (SARIMA, ETS, or Weighted Average)
3. Generate and analyze forecasts as usual

## Technical Details

### Dependencies
- `pyodbc` - ODBC database connectivity
- `sqlalchemy` - Database abstraction layer
- SQL Server ODBC drivers (already installed)

### Connection Method
- Uses Windows Authentication (`Trusted_Connection=Yes`)
- Connects to SQL Server via ODBC
- SQLAlchemy provides database abstraction

### Data Processing
1. **Query Execution**: Runs the custom SQL query
2. **Data Loading**: Loads results into pandas DataFrame
3. **Preprocessing**: Applies standard data cleaning and validation
4. **Column Mapping**: Ensures columns match app requirements
5. **Quality Checks**: Validates data completeness and format

### Error Handling
- Connection failures are caught and reported
- SQL errors are displayed with helpful messages
- Data validation issues are highlighted
- Fallback options are provided

## Customization

### Modifying the SQL Query
1. Edit `config/sales.sql` to change the data selection
2. Ensure the query returns all required columns
3. Test the query before using in the app

### Connection String Options
You can modify the connection string for different environments:
- **Production**: Current default configuration
- **Development**: Change database name to development instance
- **Different Server**: Update SERVER parameter

### Query Optimization
- The query includes date filters to limit data volume
- Aggregation reduces row count for better performance
- Indexes on TrnDate, StockCode, and Customer recommended

## Troubleshooting

### Common Issues

**Connection Fails**
- Verify SQL Server is accessible from your machine
- Check Windows Authentication permissions
- Ensure ODBC drivers are installed

**No Data Returned**
- Check date filters in the SQL query
- Verify product filters (StockCode LIKE 'F%')
- Confirm data exists in the specified time range

**Column Errors**
- Ensure SQL query returns all required columns
- Check column names match exactly
- Verify data types are compatible

**Performance Issues**
- Large datasets may take time to load
- Consider adding date range limits
- Monitor SQL Server performance

### Support
For technical issues:
1. Check the connection test results
2. Review SQL query syntax
3. Verify database permissions
4. Contact IT support for server access issues

## Security Notes
- Uses Windows Authentication for secure access
- No passwords stored in configuration
- Read-only access to sales data
- Connection string can be modified as needed

## Future Enhancements
- Support for SQL Server Authentication
- Multiple database connections
- Cached query results
- Real-time data refresh
- Custom query builder interface
