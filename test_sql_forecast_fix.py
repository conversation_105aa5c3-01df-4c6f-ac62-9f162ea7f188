#!/usr/bin/env python3
"""
Test script to create a targeted fix for SQL forecast validation
"""

import pandas as pd
import numpy as np
import sys
import os
import warnings
warnings.filterwarnings('ignore')

# Simple test without importing the full app
def create_test_sql_data():
    """Create test data that mimics SQL data characteristics"""
    
    # Create 18 months of data with high growth trend (like SQL data shows)
    periods = pd.date_range(start='2024-01-01', periods=18, freq='MS')
    
    # Base volume with strong growth trend
    base_volume = 1000
    volumes = []
    
    for i, period in enumerate(periods):
        # Strong growth trend: 50% growth over 18 months
        growth_factor = 1 + (i / 18) * 0.5  # 50% total growth
        seasonal_factor = 1.2 if period.month in [11, 12, 1] else 1.0  # Holiday boost
        noise = np.random.normal(0, 0.1)  # 10% random variation
        
        volume = base_volume * growth_factor * seasonal_factor * (1 + noise)
        volumes.append(max(volume, 100))
    
    data = pd.DataFrame({
        'Period': periods,
        'Product': 'TEST-PRODUCT',
        'Customer': 'TEST-CUSTOMER',
        'Volume': volumes,
        'NSV': [v * 15.5 for v in volumes],
        'Category': 'Test Category',
        'Description': 'Test Product',
        'SubCategory': 'Test SubCategory',
        'Name': 'Test Customer',
        'Region': 'Test Region',
        'Warehouse': '10000',
        'WHDescription': 'Test Warehouse'
    })
    
    return data

def simple_forecast(data, periods=12):
    """Simple forecast that should respect growth trends"""
    
    # Calculate basic statistics
    hist_avg = data['Volume'].mean()
    recent_6_months = data.tail(6)['Volume'].mean()
    
    # Calculate growth trend
    if len(data) >= 12:
        recent_6 = data.tail(6)['Volume'].mean()
        previous_6 = data.iloc[-12:-6]['Volume'].mean()
        growth_trend = (recent_6 - previous_6) / previous_6 if previous_6 > 0 else 0
    else:
        growth_trend = 0.1  # Default 10% growth
    
    print(f"Historical average: {hist_avg:.0f}")
    print(f"Recent 6-month average: {recent_6_months:.0f}")
    print(f"Growth trend: {growth_trend:.1%}")
    
    # Create simple forecast based on recent average with growth
    base_forecast = recent_6_months * (1 + growth_trend)
    forecast_values = [base_forecast] * periods
    
    # Create forecast dataframe
    last_period = data['Period'].max()
    future_periods = pd.date_range(
        start=last_period + pd.DateOffset(months=1),
        periods=periods,
        freq='MS'
    )
    
    forecast_df = pd.DataFrame({
        'Period': future_periods,
        'Volume': forecast_values
    })
    
    print(f"Simple forecast average: {forecast_df['Volume'].mean():.0f}")
    print(f"Forecast vs historical ratio: {forecast_df['Volume'].mean() / hist_avg:.2f}")
    print(f"Forecast vs recent ratio: {forecast_df['Volume'].mean() / recent_6_months:.2f}")
    
    return forecast_df

def test_validation_rules(forecast_df, historical_data):
    """Test the current validation rules"""
    
    print(f"\nTesting Validation Rules:")
    print("=" * 40)
    
    # Calculate historical statistics
    hist_avg = historical_data['Volume'].mean()
    hist_max = historical_data['Volume'].max()
    recent_data = historical_data.tail(6)
    recent_avg = recent_data['Volume'].mean()
    
    # Calculate growth trend
    if len(historical_data) >= 12:
        previous_6_months = historical_data.iloc[-12:-6]['Volume'].mean()
        growth_trend = (recent_avg - previous_6_months) / previous_6_months if previous_6_months > 0 else 0
    else:
        growth_trend = 0
    
    forecast_avg = forecast_df['Volume'].mean()
    forecast_max = forecast_df['Volume'].max()
    
    print(f"Historical avg: {hist_avg:.0f}")
    print(f"Recent avg: {recent_avg:.0f}")
    print(f"Growth trend: {growth_trend:.1%}")
    print(f"Forecast avg: {forecast_avg:.0f}")
    print(f"Forecast max: {forecast_max:.0f}")
    
    # Test each validation rule
    print(f"\nValidation Rule Tests:")
    
    # Rule 1: Forecast shouldn't be more than 5x historical max
    rule1_triggered = forecast_max > hist_max * 5
    print(f"Rule 1 (Cap high values): {'TRIGGERED' if rule1_triggered else 'OK'}")
    if rule1_triggered:
        print(f"  Forecast max ({forecast_max:.0f}) > 5x hist max ({hist_max * 5:.0f})")
    
    # Rule 2: Forecast shouldn't be less than 5% of historical average
    min_threshold = hist_avg * 0.05
    rule2_triggered = forecast_df['Volume'].min() < min_threshold
    print(f"Rule 2 (Raise low values): {'TRIGGERED' if rule2_triggered else 'OK'}")
    if rule2_triggered:
        print(f"  Forecast min ({forecast_df['Volume'].min():.0f}) < 5% hist avg ({min_threshold:.0f})")
    
    # Rule 3: Adjust if dramatically different from recent trends
    if growth_trend > 0.2:  # Strong growth trend
        min_recent_threshold = recent_avg * 0.3
    elif growth_trend > 0:  # Positive growth
        min_recent_threshold = recent_avg * 0.4
    else:  # Declining or flat trend
        min_recent_threshold = recent_avg * 0.5
    
    rule3_triggered = forecast_avg < min_recent_threshold
    print(f"Rule 3 (Recent trend adjustment): {'TRIGGERED' if rule3_triggered else 'OK'}")
    if rule3_triggered:
        print(f"  Forecast avg ({forecast_avg:.0f}) < threshold ({min_recent_threshold:.0f})")
        print(f"  Growth trend: {growth_trend:.1%}, threshold factor: {min_recent_threshold/recent_avg:.1f}")
    
    # Rule 4: Intelligent growth adjustment
    if growth_trend > 0.1:  # If there's positive growth, be less aggressive
        min_hist_threshold = hist_avg * 0.6
    else:  # If declining or flat, still ensure reasonable minimum
        min_hist_threshold = hist_avg * 0.5
    
    rule4_triggered = forecast_avg < min_hist_threshold
    print(f"Rule 4 (Historical adjustment): {'TRIGGERED' if rule4_triggered else 'OK'}")
    if rule4_triggered:
        print(f"  Forecast avg ({forecast_avg:.0f}) < threshold ({min_hist_threshold:.0f})")
        print(f"  Growth trend: {growth_trend:.1%}, threshold factor: {min_hist_threshold/hist_avg:.1f}")
    
    return {
        'rule1': rule1_triggered,
        'rule2': rule2_triggered,
        'rule3': rule3_triggered,
        'rule4': rule4_triggered,
        'growth_trend': growth_trend
    }

def suggest_improved_validation():
    """Suggest improved validation rules"""
    
    print(f"\n" + "=" * 60)
    print("IMPROVED VALIDATION SUGGESTIONS")
    print("=" * 60)
    
    print("""
    Current Issues:
    1. Rules are too aggressive for high-growth scenarios
    2. Validation doesn't properly account for strong positive trends
    3. Thresholds are too conservative for SQL data patterns
    
    Suggested Improvements:
    1. Make validation growth-aware
    2. Use dynamic thresholds based on trend strength
    3. Allow higher forecasts for strong growth patterns
    4. Reduce minimum adjustment factors
    
    Proposed New Rules:
    - Rule 3: For strong growth (>20%), allow forecasts down to 20% of recent
    - Rule 4: For positive growth (>10%), allow forecasts down to 40% of historical
    - Reduce adjustment factors from 70-80% to 60-70%
    - Add growth momentum factor to forecasts
    """)

def main():
    print("SQL Forecast Validation Fix Test")
    print("=" * 60)
    
    # Create test data with high growth (like SQL data)
    test_data = create_test_sql_data()
    print(f"Created test data: {len(test_data)} months")
    print(f"Volume trend: {test_data['Volume'].iloc[0]:.0f} -> {test_data['Volume'].iloc[-1]:.0f}")
    print(f"Total growth: {(test_data['Volume'].iloc[-1] / test_data['Volume'].iloc[0] - 1):.1%}")
    
    # Generate simple forecast
    print(f"\nGenerating Simple Forecast:")
    forecast = simple_forecast(test_data, periods=12)
    
    # Test validation rules
    validation_results = test_validation_rules(forecast, test_data)
    
    # Show results
    print(f"\nValidation Summary:")
    triggered_rules = sum(validation_results[key] for key in ['rule1', 'rule2', 'rule3', 'rule4'])
    print(f"Rules triggered: {triggered_rules}/4")
    
    if triggered_rules > 0:
        print("⚠️ Validation would modify this reasonable forecast!")
    else:
        print("✅ Validation would accept this forecast")
    
    # Suggest improvements
    suggest_improved_validation()

if __name__ == "__main__":
    main()
