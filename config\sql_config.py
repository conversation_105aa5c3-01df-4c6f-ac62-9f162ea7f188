# SQL Server Configuration for Sales Forecasting App

# Default SQL Server connection string
DEFAULT_CONNECTION_STRING = """DRIVER=SQL Server;SERVER=*************;UID=syspro_report;Trusted_Connection=Yes;APP=2007 Microsoft Office system;WSID=PFLPOM0083;DATABASE=SysproCompanyPAR"""

# SQL file path
SQL_FILE_PATH = "config/sales.sql"

# Connection timeout settings
CONNECTION_TIMEOUT = 30  # seconds
QUERY_TIMEOUT = 300     # seconds (5 minutes)

# Data validation settings
REQUIRED_COLUMNS = [
    'Customer', 'Name', 'Product', 'Description', 'Category', 'SubCategory',
    'Region', 'Warehouse', 'WHDescription', 'Period', 'Volume', 'NSV'
]

# Data quality checks
MIN_RECORDS_THRESHOLD = 100  # Minimum number of records expected
MAX_RECORDS_THRESHOLD = 1000000  # Maximum number of records to prevent memory issues

# SQL Server connection parameters
SQL_SERVER_CONFIG = {
    'server': '*************',
    'database': 'SysproCompanyPAR',
    'username': 'syspro_report',
    'trusted_connection': True,
    'driver': 'SQL Server',
    'application_name': '2007 Microsoft Office system',
    'workstation_id': 'PFLPOM0083'
}

# Alternative connection strings for different environments
CONNECTION_STRINGS = {
    'production': DEFAULT_CONNECTION_STRING,
    'development': DEFAULT_CONNECTION_STRING.replace('SysproCompanyPAR', 'SysproCompanyDEV'),
    'test': DEFAULT_CONNECTION_STRING.replace('SysproCompanyPAR', 'SysproCompanyTEST')
}

# Query optimization settings
QUERY_SETTINGS = {
    'chunk_size': 10000,  # Number of rows to fetch at a time
    'use_fast_executemany': True,  # For bulk operations
    'autocommit': True
}
