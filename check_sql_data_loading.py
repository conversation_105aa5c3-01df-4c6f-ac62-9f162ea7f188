#!/usr/bin/env python3
"""
Check SQL data loading to verify 2025 data is being captured correctly
"""

import pandas as pd
import numpy as np
import sys
import os
import warnings
warnings.filterwarnings('ignore')

def create_sql_server_connection(connection_string):
    """Create SQL Server connection using SQLAlchemy"""
    try:
        from sqlalchemy import create_engine
        # Build SQLAlchemy connection string for Windows Authentication
        connection_url = "mssql+pyodbc://@10.101.102.50/SysproCompanyPAR?driver=SQL+Server&trusted_connection=yes"
        engine = create_engine(connection_url)
        return engine
    except Exception as e:
        print(f"Error creating SQL Server connection: {str(e)}")
        return None

def load_sql_query(file_path):
    """Load SQL query from file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except Exception as e:
        print(f"Error loading SQL file: {str(e)}")
        return None

def load_and_analyze_sql_data():
    """Load SQL data and analyze 2025 coverage"""
    
    print("SQL Data Loading Analysis")
    print("=" * 60)
    
    # Connection details
    connection_string = "DRIVER=SQL Server;SERVER=10.101.102.50;UID=syspro_report;Trusted_Connection=Yes;APP=2007 Microsoft Office system;WSID=PFLPOM0083;DATABASE=SysproCompanyPAR"
    sql_file_path = os.path.join("config", "sales.sql")
    
    # Load SQL query
    if not os.path.exists(sql_file_path):
        print(f"❌ SQL file not found: {sql_file_path}")
        return
    
    sql_query = load_sql_query(sql_file_path)
    if not sql_query:
        print("❌ Failed to load SQL query")
        return
    
    print("✅ SQL query loaded successfully")
    print(f"Query length: {len(sql_query)} characters")
    
    # Create connection
    engine = create_sql_server_connection(connection_string)
    if engine is None:
        print("❌ Failed to create database connection")
        return
    
    print("✅ Database connection established")
    
    try:
        # Execute query and load data
        print("\n📊 Executing SQL query...")
        df = pd.read_sql(sql_query, engine)
        
        # Close connection
        engine.dispose()
        
        print(f"✅ Data loaded: {len(df)} records")
        
        if df.empty:
            print("❌ No data returned from SQL query")
            return
        
        # Convert Period to datetime for analysis
        df['Period'] = pd.to_datetime(df['Period'], format='%Y-%m')
        
        # Basic data analysis
        print(f"\n📈 Data Overview:")
        print(f"Date range: {df['Period'].min()} to {df['Period'].max()}")
        print(f"Total Volume: {df['Volume'].sum():,.0f}")
        print(f"Total NSV: {df['NSV'].sum():,.0f}")
        print(f"Unique products: {df['Product'].nunique()}")
        print(f"Unique customers: {df['Customer'].nunique()}")
        
        # 2025 specific analysis
        print(f"\n🎯 2025 Data Analysis:")
        df_2025 = df[df['Period'].dt.year == 2025]
        
        if df_2025.empty:
            print("❌ No 2025 data found!")
            return
        
        print(f"2025 records: {len(df_2025)}")
        print(f"2025 date range: {df_2025['Period'].min()} to {df_2025['Period'].max()}")
        print(f"2025 Total Volume: {df_2025['Volume'].sum():,.0f}")
        print(f"2025 Total NSV: {df_2025['NSV'].sum():,.0f}")
        
        # Monthly breakdown for 2025
        print(f"\n📅 2025 Monthly Breakdown:")
        monthly_2025 = df_2025.groupby(df_2025['Period'].dt.strftime('%Y-%m')).agg({
            'Volume': 'sum',
            'NSV': 'sum'
        }).reset_index()
        
        for _, row in monthly_2025.iterrows():
            print(f"  {row['Period']}: Volume={row['Volume']:,.0f}, NSV={row['NSV']:,.0f}")
        
        # Expected vs actual check
        print(f"\n🔍 Expected vs Actual Check:")
        expected_volume = 5273225
        expected_nsv = 221000000  # 221M
        
        actual_volume = df_2025['Volume'].sum()
        actual_nsv = df_2025['NSV'].sum()
        
        print(f"Expected 2025 Volume: {expected_volume:,.0f}")
        print(f"Actual 2025 Volume: {actual_volume:,.0f}")
        print(f"Volume difference: {actual_volume - expected_volume:,.0f} ({((actual_volume/expected_volume-1)*100):+.1f}%)")
        
        print(f"\nExpected 2025 NSV: {expected_nsv:,.0f}")
        print(f"Actual 2025 NSV: {actual_nsv:,.0f}")
        print(f"NSV difference: {actual_nsv - expected_nsv:,.0f} ({((actual_nsv/expected_nsv-1)*100):+.1f}%)")
        
        # Check for potential data issues
        print(f"\n🔧 Data Quality Checks:")
        
        # Check for missing months in 2025
        expected_months = pd.date_range('2025-01', '2025-07', freq='MS')
        actual_months = df_2025['Period'].dt.to_period('M').unique()
        missing_months = []
        
        for month in expected_months:
            if month.to_period('M') not in actual_months:
                missing_months.append(month.strftime('%Y-%m'))
        
        if missing_months:
            print(f"⚠️ Missing months in 2025: {', '.join(missing_months)}")
        else:
            print(f"✅ All expected months present (Jan-Jul 2025)")
        
        # Check for zero/negative values
        zero_volume = len(df_2025[df_2025['Volume'] <= 0])
        zero_nsv = len(df_2025[df_2025['NSV'] <= 0])
        
        if zero_volume > 0:
            print(f"⚠️ Records with zero/negative volume: {zero_volume}")
        if zero_nsv > 0:
            print(f"⚠️ Records with zero/negative NSV: {zero_nsv}")
        
        # Check preprocessing impact
        print(f"\n🔄 Preprocessing Impact Check:")
        
        # Simulate preprocessing
        df_processed = df.copy()
        
        # Remove zero or negative volumes (as done in preprocess_data)
        before_count = len(df_processed)
        df_processed = df_processed[df_processed['Volume'] > 0]
        after_volume_filter = len(df_processed)
        
        # Remove zero or negative NSV
        df_processed = df_processed[df_processed['NSV'] > 0]
        after_nsv_filter = len(df_processed)
        
        print(f"Original records: {before_count:,}")
        print(f"After volume filter: {after_volume_filter:,} (removed {before_count - after_volume_filter:,})")
        print(f"After NSV filter: {after_nsv_filter:,} (removed {after_volume_filter - after_nsv_filter:,})")
        
        # 2025 totals after preprocessing
        df_2025_processed = df_processed[df_processed['Period'].dt.year == 2025]
        print(f"\n2025 totals after preprocessing:")
        print(f"Volume: {df_2025_processed['Volume'].sum():,.0f}")
        print(f"NSV: {df_2025_processed['NSV'].sum():,.0f}")
        
        return df_2025_processed
        
    except Exception as e:
        print(f"❌ Error executing SQL query: {str(e)}")
        engine.dispose()
        return None

if __name__ == "__main__":
    load_and_analyze_sql_data()
