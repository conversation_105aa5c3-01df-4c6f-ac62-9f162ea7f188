# Enhanced Sales Forecasting App - Version 2
# Focuses on better model-based forecasting

import streamlit as st
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.statespace.sarimax import SARIMAX
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
from statsmodels.tools.sm_exceptions import ConvergenceWarning
import io
import pyodbc
import sqlalchemy
from sqlalchemy import create_engine

# Suppress warnings
warnings.simplefilter('ignore', ConvergenceWarning)
warnings.filterwarnings('ignore')

# Increase pandas styler max elements
pd.set_option('styler.render.max_elements', 1000000)

def format_currency(value):
    """Format currency values in PNG Kina with appropriate symbols and decimal places"""
    if pd.isna(value):
        return "PGK 0"
    abs_value = abs(value)
    if abs_value >= 1_000_000_000:
        return f"PGK {value/1_000_000_000:.1f}B"
    elif abs_value >= 1_000_000:
        return f"PGK {value/1_000_000:.1f}M"
    elif abs_value >= 1_000:
        return f"PGK {value/1_000:.1f}K"
    else:
        return f"PGK {value:,.0f}"

def format_number(value, decimal_places=1):
    """Format large numbers with appropriate suffixes"""
    if pd.isna(value):
        return "0"
    abs_value = abs(value)
    if abs_value >= 1_000_000_000:
        return f"{value/1_000_000_000:.{decimal_places}f}B"
    elif abs_value >= 1_000_000:
        return f"{value/1_000_000:.{decimal_places}f}M"
    elif abs_value >= 1_000:
        return f"{value/1_000:.{decimal_places}f}K"
    else:
        return f"{value:,.0f}"

def prepare_combined_data_for_download(forecast_df, historical_df=None):
    """Prepare combined historical and forecast data for download"""
    combined_data = []

    # Add historical data if available
    if historical_df is not None and not historical_df.empty:
        hist_df = historical_df.copy()
        hist_df['DataType'] = 'Historical'
        hist_df['is_forecast'] = False
        combined_data.append(hist_df)

    # Add forecast data
    if not forecast_df.empty:
        fore_df = forecast_df.copy()
        fore_df['DataType'] = 'Forecast'
        fore_df['is_forecast'] = True
        combined_data.append(fore_df)

    if not combined_data:
        return pd.DataFrame()

    # Combine all data
    full_df = pd.concat(combined_data, ignore_index=True)

    # Sort by Product, Customer, Period
    full_df = full_df.sort_values(['Product', 'Customer', 'Period'])

    return prepare_forecast_for_download(full_df)

def prepare_forecast_for_download(forecast_df):
    """Prepare forecast data for download with proper column mapping and formatting"""
    if forecast_df.empty:
        return pd.DataFrame()

    # Create a copy to avoid modifying the original
    download_df = forecast_df.copy()

    # Ensure all required columns exist (including Region and Category)
    required_cols = ['Period', 'Product', 'Description', 'Customer', 'Name',
                    'Warehouse', 'WHDescription', 'Region', 'Category', 'Volume', 'NSV']

    for col in required_cols:
        if col not in download_df.columns:
            download_df[col] = ''

    # Add DataType and is_forecast columns if they exist
    if 'DataType' in forecast_df.columns:
        required_cols.append('DataType')
    if 'is_forecast' in forecast_df.columns:
        required_cols.append('is_forecast')

    # Select columns and apply column mapping for renamed columns
    download_df = download_df[required_cols].copy()

    # Rename columns according to export mapping (only for columns that need renaming)
    rename_mapping = {k: v for k, v in EXPORT_COLUMN_MAPPING.items() if k in download_df.columns}
    download_df = download_df.rename(columns=rename_mapping)

    # Format the Period column
    download_df['Period'] = pd.to_datetime(download_df['Period']).dt.strftime('%Y-%m')

    # Round numerical values
    if 'ForecastQty' in download_df.columns:
        download_df['ForecastQty'] = download_df['ForecastQty'].round(2)
    if 'ForecastValue' in download_df.columns:
        download_df['ForecastValue'] = download_df['ForecastValue'].round(2)

    # Reorder columns to match user specification (including Region and Category)
    column_order = ['Stock', 'StockDescription', 'Customer', 'Name',
                   'Warehouse', 'WhDescription', 'Region', 'Category',
                   'Period', 'ForecastQty', 'ForecastValue']

    # Only include columns that exist
    available_columns = [col for col in column_order if col in download_df.columns]
    download_df = download_df[available_columns]

    return download_df

def create_download_link(df, filename, file_format='csv', category_splits_data=None):
    """Create a download link for the forecast data with optional category splits"""
    if df.empty:
        return None

    if file_format.lower() == 'csv':
        # Create CSV (only main forecast data for CSV)
        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, index=False)
        csv_data = csv_buffer.getvalue()
        return csv_data.encode('utf-8')

    elif file_format.lower() == 'excel':
        # Create Excel with multiple sheets
        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            # Main forecast data
            df.to_excel(writer, sheet_name='Forecast Data', index=False)

            # Add category splits if provided
            if category_splits_data:
                # Category monthly splits
                if 'category_monthly' in category_splits_data:
                    category_splits_data['category_monthly'].to_excel(
                        writer, sheet_name='Category Monthly', index=False
                    )

                # Category totals
                if 'category_totals' in category_splits_data:
                    category_splits_data['category_totals'].to_excel(
                        writer, sheet_name='Category Totals', index=False
                    )

                # Monthly totals
                if 'monthly_totals' in category_splits_data:
                    category_splits_data['monthly_totals'].to_excel(
                        writer, sheet_name='Monthly Totals', index=False
                    )

        return excel_buffer.getvalue()

    return None

def load_sql_query(sql_file_path):
    """Load SQL query from file"""
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except Exception as e:
        st.error(f"Error loading SQL file: {str(e)}")
        return None

def create_sql_server_connection(connection_string):
    """Create SQL Server connection using SQLAlchemy"""
    try:
        # Convert ODBC connection string to SQLAlchemy format
        # Parse the connection string components
        params = {}
        for param in connection_string.split(';'):
            if '=' in param:
                key, value = param.split('=', 1)
                params[key.strip()] = value.strip()

        # Build SQLAlchemy connection string
        server = params.get('SERVER', '')
        database = params.get('DATABASE', '')
        uid = params.get('UID', '')

        if params.get('Trusted_Connection', '').lower() == 'yes':
            # Windows Authentication
            connection_url = f"mssql+pyodbc://@{server}/{database}?driver=SQL+Server&trusted_connection=yes"
        else:
            # SQL Server Authentication (if needed)
            connection_url = f"mssql+pyodbc://{uid}@{server}/{database}?driver=SQL+Server"

        engine = create_engine(connection_url)
        return engine
    except Exception as e:
        st.error(f"Error creating SQL Server connection: {str(e)}")
        return None

def load_data_from_sql_server(connection_string, sql_query):
    """Load data from SQL Server using the provided connection string and query"""
    try:
        engine = create_sql_server_connection(connection_string)
        if engine is None:
            return pd.DataFrame()

        # Execute query and load data
        with st.spinner("Connecting to SQL Server and loading data..."):
            df = pd.read_sql(sql_query, engine)

        # Close the connection
        engine.dispose()

        return df
    except Exception as e:
        st.error(f"Error loading data from SQL Server: {str(e)}")
        return pd.DataFrame()

def test_sql_server_connection(connection_string):
    """Test SQL Server connection"""
    try:
        engine = create_sql_server_connection(connection_string)
        if engine is None:
            return False, "Failed to create connection"

        # Test with a simple query
        with engine.connect() as conn:
            result = conn.execute(sqlalchemy.text("SELECT 1 as test"))
            result.fetchone()

        engine.dispose()
        return True, "Connection successful"
    except Exception as e:
        return False, f"Connection failed: {str(e)}"

# Required columns for the application - must match CSV file exactly
REQUIRED_COLUMNS = [
    'Customer', 'Name', 'Product', 'Description', 'Category', 'SubCategory',
    'Region', 'Warehouse', 'WHDescription', 'Period', 'Volume', 'NSV'
]

# Column mapping for download export (maps internal names to user-requested names)
EXPORT_COLUMN_MAPPING = {
    'Product': 'Stock',
    'Description': 'StockDescription',
    'Customer': 'Customer',
    'Name': 'Name',
    'Warehouse': 'Warehouse',
    'WHDescription': 'WhDescription',
    'Volume': 'ForecastQty',
    'NSV': 'ForecastValue'
}

def standardize_period_column(df):
    """Standardize the Period column to datetime, handling YYYY-MM format"""
    if 'Period' in df.columns:
        if not pd.api.types.is_datetime64_any_dtype(df['Period']):
            # Handle YYYY-MM format by appending '-01' to make it a valid date
            df['Period'] = pd.to_datetime(df['Period'].astype(str) + '-01')
    return df

def validate_and_report_data_quality(df):
    """Validate data quality and provide detailed reporting"""
    if df.empty:
        return df, {}

    report = {}
    original_count = len(df)
    report['original_records'] = original_count

    # Check for negative values
    negative_volume = (df['Volume'] <= 0).sum() if 'Volume' in df.columns else 0
    negative_nsv = (df['NSV'] <= 0).sum() if 'NSV' in df.columns else 0
    report['negative_volume'] = negative_volume
    report['negative_nsv'] = negative_nsv

    # Check for null values in critical columns
    critical_cols = ['Product', 'Customer', 'Period', 'Volume', 'NSV']
    null_counts = {}
    for col in critical_cols:
        if col in df.columns:
            null_counts[col] = df[col].isnull().sum()
    report['null_counts'] = null_counts

    # Check for duplicate records (same Product, Customer, Period)
    if all(col in df.columns for col in ['Product', 'Customer', 'Period']):
        duplicates = df.duplicated(subset=['Product', 'Customer', 'Period']).sum()
        report['duplicates'] = duplicates
    else:
        report['duplicates'] = 0

    # Date range analysis
    if 'Period' in df.columns:
        try:
            df_temp = standardize_period_column(df.copy())
            report['date_range'] = {
                'min_date': df_temp['Period'].min(),
                'max_date': df_temp['Period'].max(),
                'unique_periods': df_temp['Period'].nunique()
            }
        except:
            report['date_range'] = {'error': 'Could not parse dates'}

    return df, report

def get_customer_warehouse_mapping(df):
    """Create a mapping of customers to their most frequently used warehouse"""
    if df.empty or 'Customer' not in df.columns or 'Warehouse' not in df.columns:
        return {}

    # Calculate warehouse usage by customer (weighted by volume)
    warehouse_usage = df.groupby(['Customer', 'Warehouse', 'WHDescription']).agg({
        'Volume': 'sum',
        'NSV': 'sum'
    }).reset_index()

    # Find the most used warehouse per customer (by volume)
    customer_warehouse = warehouse_usage.loc[
        warehouse_usage.groupby('Customer')['Volume'].idxmax()
    ][['Customer', 'Warehouse', 'WHDescription']]

    # Create mapping dictionary
    warehouse_mapping = {}
    for _, row in customer_warehouse.iterrows():
        warehouse_mapping[row['Customer']] = {
            'Warehouse': row['Warehouse'],
            'WHDescription': row['WHDescription']
        }

    return warehouse_mapping

def preserve_transaction_history(df):
    """Preserve all transaction history while normalizing to monthly periods and adding sequence numbers"""
    if df.empty:
        return df

    # Ensure Period is datetime and normalize to first day of month
    df = standardize_period_column(df)
    df['Period'] = df['Period'].dt.to_period('M').dt.to_timestamp()

    # Clean string columns to handle null/empty values
    string_cols = ['Customer', 'Name', 'Product', 'Description', 'Category',
                   'SubCategory', 'Region', 'Warehouse', 'WHDescription']

    for col in string_cols:
        if col in df.columns:
            df[col] = df[col].astype(str).str.strip()
            df[col] = df[col].replace(['nan', 'None', '', 'null'], 'Unknown')

    # Add transaction sequence number to preserve duplicate history
    # This ensures each transaction within the same Product-Customer-Period is unique
    df['TransactionSeq'] = df.groupby(['Product', 'Customer', 'Period']).cumcount() + 1

    # Get customer-warehouse mapping before any changes
    customer_warehouse_map = get_customer_warehouse_mapping(df)

    # Apply warehouse mapping based on historical customer patterns
    for idx, row in df.iterrows():
        customer = row['Customer']
        if customer in customer_warehouse_map:
            df.at[idx, 'Warehouse'] = customer_warehouse_map[customer]['Warehouse']
            df.at[idx, 'WHDescription'] = customer_warehouse_map[customer]['WHDescription']

    # Sort by Product, Customer, Period, and Transaction Sequence for consistent ordering
    df = df.sort_values(['Product', 'Customer', 'Period', 'TransactionSeq'])

    # Reset index to ensure clean DataFrame
    df = df.reset_index(drop=True)

    return df

def aggregate_to_monthly(df):
    """Aggregate data to monthly level by Product and Customer, with option to preserve transaction history"""
    if df.empty:
        return df

    # Ensure Period is datetime and normalize to first day of month
    df = standardize_period_column(df)
    df['Period'] = df['Period'].dt.to_period('M').dt.to_timestamp()

    # Clean string columns to handle null/empty values
    string_cols = ['Customer', 'Name', 'Product', 'Description', 'Category',
                   'SubCategory', 'Region', 'Warehouse', 'WHDescription']

    for col in string_cols:
        if col in df.columns:
            df[col] = df[col].astype(str).str.strip()
            df[col] = df[col].replace(['nan', 'None', '', 'null'], 'Unknown')

    # Get customer-warehouse mapping before aggregation
    customer_warehouse_map = get_customer_warehouse_mapping(df)

    # Primary aggregation: Group by Product, Customer, and Period (monthly splits)
    primary_group_cols = ['Product', 'Customer', 'Period']

    # Prepare aggregation dictionary
    agg_dict = {
        'Volume': 'sum',
        'NSV': 'sum',
        # Take the first occurrence for metadata (will be overridden by warehouse mapping)
        'Category': 'first',
        'Description': 'first',
        'SubCategory': 'first',
        'Name': 'first',
        'Region': 'first',
        'Warehouse': 'first',
        'WHDescription': 'first'
    }

    # Add transaction count if TransactionSeq column exists
    if 'TransactionSeq' in df.columns:
        agg_dict['TransactionSeq'] = 'count'

    # Aggregate Volume and NSV by Product-Customer-Month
    df_agg = df.groupby(primary_group_cols, as_index=False).agg(agg_dict)

    # Rename transaction count column for clarity if it exists
    if 'TransactionSeq' in df_agg.columns:
        df_agg = df_agg.rename(columns={'TransactionSeq': 'TransactionCount'})

    # Apply warehouse mapping based on historical customer patterns
    for idx, row in df_agg.iterrows():
        customer = row['Customer']
        if customer in customer_warehouse_map:
            df_agg.at[idx, 'Warehouse'] = customer_warehouse_map[customer]['Warehouse']
            df_agg.at[idx, 'WHDescription'] = customer_warehouse_map[customer]['WHDescription']

    # Sort by Product, Customer, and Period for consistent ordering
    df_agg = df_agg.sort_values(['Product', 'Customer', 'Period'])

    # Reset index to ensure clean DataFrame
    df_agg = df_agg.reset_index(drop=True)

    return df_agg

def preprocess_data(df):
    """Complete data preprocessing pipeline with enhanced validation and filtering"""
    if df.empty:
        return df

    # Validate data quality and get report
    df, quality_report = validate_and_report_data_quality(df)

    # Create a mapping of lowercase column names to their original case
    col_mapping = {col.lower(): col for col in df.columns}

    # Check for missing columns (case-insensitive check)
    missing_cols = []
    for req_col in REQUIRED_COLUMNS:
        if req_col.lower() not in col_mapping:
            missing_cols.append(req_col)

    if missing_cols:
        st.error(f"Missing required columns: {', '.join(missing_cols)}")
        return pd.DataFrame()

    # Standardize column names to match REQUIRED_COLUMNS exactly
    df = df.rename(columns={col_mapping[req_col.lower()]: req_col
                          for req_col in REQUIRED_COLUMNS
                          if req_col.lower() in col_mapping})

    # Standardize period column
    df = standardize_period_column(df)

    # Filter out negative values and invalid records before aggregation
    original_count = len(df)

    # Remove records with negative or zero Volume or NSV
    df = df[(df['Volume'] > 0) & (df['NSV'] > 0)].copy()

    # Remove records with null values in critical columns
    critical_cols = ['Product', 'Customer', 'Period', 'Volume', 'NSV']
    df = df.dropna(subset=critical_cols)

    filtered_count = len(df)

    # Store quality report for consolidated display later
    if original_count != filtered_count:
        st.info(f"📊 Data cleaning applied: {original_count - filtered_count:,} records filtered out")

    # Check for duplicate transactions before processing
    original_records = len(df)
    unique_combinations = df[['Product', 'Customer', 'Period']].drop_duplicates().shape[0]
    duplicate_transactions = original_records - unique_combinations

    # Process data based on duplicate detection
    if duplicate_transactions > 0:
        # Preserve all transaction history with sequence numbers
        df_preserved = preserve_transaction_history(df)
        # Store preserved data for detailed analysis
        st.session_state.preserved_transactions = df_preserved
        # Create aggregated version for forecasting
        df = aggregate_to_monthly(df_preserved)
    else:
        df = aggregate_to_monthly(df)

    # Consolidated Data Quality Report
    if not df.empty:
        # Calculate all statistics
        unique_products = df['Product'].nunique()
        unique_customers = df['Customer'].nunique()
        unique_combinations_final = df[['Product', 'Customer']].drop_duplicates().shape[0]
        unique_warehouses = df['Warehouse'].nunique() if 'Warehouse' in df.columns else 0

        # Date range info
        date_info = ""
        if 'Period' in df.columns:
            min_date = df['Period'].min().strftime('%Y-%m')
            max_date = df['Period'].max().strftime('%Y-%m')
            date_info = f"📅 **Date Range:** {min_date} to {max_date}"

        # Transaction info
        transaction_info = ""
        if 'TransactionCount' in df.columns:
            total_transactions = df['TransactionCount'].sum()
            avg_transactions = df['TransactionCount'].mean()
            transaction_info = f"📋 **Transaction Details:** {total_transactions:,} total transactions (avg {avg_transactions:.1f} per month)"

        # Quality report details
        quality_details = ""
        if quality_report:
            negative_total = quality_report.get('negative_volume', 0) + quality_report.get('negative_nsv', 0)
            null_total = sum(quality_report.get('null_counts', {}).values())
            duplicates_found = quality_report.get('duplicates', 0)

            quality_details = f"""
            **Data Cleaning Applied:**
            - Records with negative/zero values removed: {negative_total:,}
            - Records with missing critical data removed: {null_total:,}
            - Duplicate records detected: {duplicates_found:,}
            """

        # Duplicate processing info
        duplicate_info = ""
        if duplicate_transactions > 0:
            duplicate_info = f"""
            **Duplicate Transaction Handling:**
            - ✅ **PRESERVED:** All {duplicate_transactions:,} duplicate transactions retained with sequence numbers
            - 📊 **AGGREGATED:** Volumes summed by month for forecasting
            - 🔍 **AVAILABLE:** Complete transaction history accessible for download
            """

        # Store quality report information in session state for later display
        st.session_state.data_quality_report = {
            'original_records': original_records,
            'final_records': len(df),
            'unique_products': unique_products,
            'unique_customers': unique_customers,
            'unique_combinations_final': unique_combinations_final,
            'unique_warehouses': unique_warehouses,
            'date_info': date_info,
            'transaction_info': transaction_info,
            'quality_details': quality_details,
            'duplicate_info': duplicate_info
        }

    return df

def analyze_historical_patterns(data):
    """Analyze historical patterns to better inform forecasting"""
    data = data.sort_values('Period')

    # Calculate various growth metrics
    data['Year'] = data['Period'].dt.year
    data['Month'] = data['Period'].dt.month

    # Historical averages and trends
    historical_avg = data['Volume'].mean()
    recent_avg = data['Volume'].tail(6).mean()  # Last 6 months

    # Year-over-year growth (more robust calculation)
    yearly_totals = data.groupby('Year')['Volume'].sum()
    if len(yearly_totals) > 1:
        yearly_growth = yearly_totals.pct_change().dropna()
        # Use median growth to avoid outliers
        avg_yearly_growth = yearly_growth.median() if not yearly_growth.empty else 0.05
        # Cap growth between -20% and 50% for realism
        avg_yearly_growth = max(-0.2, min(0.5, avg_yearly_growth))
    else:
        avg_yearly_growth = 0.05

    # Monthly seasonality (more robust)
    monthly_patterns = data.groupby('Month')['Volume'].agg(['mean', 'std', 'count'])
    seasonal_factors = monthly_patterns['mean'] / monthly_patterns['mean'].mean()

    # Trend analysis (last 12 months vs previous 12 months)
    if len(data) >= 24:
        recent_12 = data.tail(12)['Volume'].sum()
        previous_12 = data.iloc[-24:-12]['Volume'].sum()
        trend_factor = recent_12 / previous_12 if previous_12 > 0 else 1.0
    else:
        trend_factor = 1.0 + avg_yearly_growth

    return {
        'historical_avg': historical_avg,
        'recent_avg': recent_avg,
        'yearly_growth': avg_yearly_growth,
        'seasonal_factors': seasonal_factors,
        'trend_factor': trend_factor,
        'data_points': len(data)
    }

def enhanced_sarima_forecast(data, periods=12):
    """
    Enhanced SARIMA forecast with better historical pattern analysis
    """
    try:
        # Analyze historical patterns
        patterns = analyze_historical_patterns(data)

        # Sort data by Period
        data = data.sort_values('Period')
        
        # Use historical patterns for better forecasting
        seasonal_factors = patterns['seasonal_factors']
        yearly_growth = patterns['yearly_growth']
        historical_avg = patterns['historical_avg']
        recent_avg = patterns['recent_avg']
        trend_factor = patterns['trend_factor']

        # Try different SARIMA parameters
        best_aic = np.inf
        best_forecast = None

        # Define parameter grid to search
        p_values = [0, 1, 2]
        d_values = [0, 1]
        q_values = [0, 1, 2]

        # Simple parameter search (can be expanded)
        for p in p_values:
            for d in d_values:
                for q in q_values:
                    try:
                        model = SARIMAX(
                            data['Volume'],
                            order=(p, d, q),
                            seasonal_order=(1, 1, 1, 12),
                            enforce_stationarity=False,
                            enforce_invertibility=False
                        )

                        with warnings.catch_warnings():
                            warnings.simplefilter("ignore")
                            model_fit = model.fit(disp=False)

                            if model_fit.aic < best_aic:
                                best_aic = model_fit.aic
                                best_forecast = model_fit.forecast(steps=periods)
                    except:
                        continue

        # If SARIMA fails, use historical pattern-based forecast
        if best_forecast is None:
            # Use recent average with seasonal patterns and growth
            base_forecast = recent_avg if recent_avg > 0 else historical_avg
            best_forecast = np.full(periods, base_forecast)

        # Ensure SARIMA forecast is not unreasonably low compared to recent performance
        # This is especially important for SQL data with strong growth trends
        sarima_avg = np.mean(best_forecast)
        if sarima_avg < recent_avg * 0.5:  # SARIMA forecast is less than 50% of recent average
            # Blend SARIMA with recent average to prevent unrealistic drops
            blend_factor = 0.7  # 70% recent average, 30% SARIMA
            blended_forecast = recent_avg * blend_factor + sarima_avg * (1 - blend_factor)
            adjustment_ratio = blended_forecast / sarima_avg if sarima_avg > 0 else 1.0
            best_forecast = best_forecast * adjustment_ratio

        # Create future periods
        last_period = data['Period'].max()
        future_periods = pd.date_range(
            start=last_period + pd.DateOffset(months=1),
            periods=periods,
            freq='MS'
        )

        # Create forecast DataFrame
        forecast_df = pd.DataFrame({
            'Period': future_periods,
            'Month': future_periods.month,
            'Volume': best_forecast.values if hasattr(best_forecast, 'values') else best_forecast
        })

        # Apply seasonal adjustments based on historical patterns
        forecast_df = forecast_df.merge(
            seasonal_factors.rename('SeasonalFactor'),
            left_on='Month',
            right_index=True,
            how='left'
        )

        # Fill any missing seasonal factors with 1.0
        forecast_df['SeasonalFactor'] = forecast_df['SeasonalFactor'].fillna(1.0)

        # Apply seasonal adjustment (but cap the impact)
        # Limit seasonal factors to reasonable range to prevent extreme adjustments
        forecast_df['SeasonalFactor'] = forecast_df['SeasonalFactor'].clip(0.5, 2.0)
        forecast_df['Volume'] = forecast_df['Volume'] * forecast_df['SeasonalFactor']

        # Apply growth trend (more balanced approach)
        # Cap yearly growth to prevent unrealistic projections
        capped_yearly_growth = max(-0.2, min(0.25, yearly_growth))  # Between -20% and +25%
        growth_multiplier = (1 + capped_yearly_growth) ** (np.arange(periods) / 12 + 1)
        forecast_df['Volume'] = forecast_df['Volume'] * growth_multiplier

        # Apply trend factor more conservatively
        # If trend factor is very low, don't apply it fully to prevent over-correction
        adjusted_trend_factor = max(0.7, min(1.5, trend_factor))  # Between 0.7 and 1.5
        forecast_df['Volume'] = forecast_df['Volume'] * adjusted_trend_factor

        # Ensure forecast doesn't fall below a reasonable minimum
        # Use 30% of recent average as absolute minimum
        min_forecast = recent_avg * 0.3
        forecast_df['Volume'] = forecast_df['Volume'].clip(lower=min_forecast)
        
        # Calculate NSV using current pricing (no price assumptions)
        unit_nsv = (data['NSV'] / data['Volume']).replace([np.inf, -np.inf], np.nan).mean()
        forecast_df['NSV'] = forecast_df['Volume'] * unit_nsv  # Use current pricing
        
        # Validate and adjust forecast against historical data
        forecast_df = validate_and_adjust_forecast(forecast_df, data)

        # Add metadata - include all required columns for complete forecast output
        metadata_cols = ['Product', 'Customer', 'Category', 'Description', 'SubCategory',
                        'Name', 'Region', 'Warehouse', 'WHDescription']
        for col in metadata_cols:
            if col in data.columns:
                forecast_df[col] = data[col].iloc[0]

        return forecast_df[['Period', 'Product', 'Customer', 'Volume', 'NSV', 'Category',
                          'Description', 'SubCategory', 'Name', 'Region', 'Warehouse', 'WHDescription']]
        
    except Exception as e:
        print(f"Error in enhanced_sarima_forecast: {str(e)}")
        return pd.DataFrame()

def enhanced_exponential_smoothing_forecast(data, periods=12):
    """Enhanced Exponential Smoothing forecast with historical pattern analysis"""
    try:
        # Analyze historical patterns first
        patterns = analyze_historical_patterns(data)

        # Sort data by Period
        data = data.sort_values('Period')

        # Set Period as index
        data_indexed = data.set_index('Period')

        # Resample to ensure regular intervals
        data_indexed = data_indexed.asfreq('MS')

        # Try different models
        best_aic = np.inf
        best_forecast = None

        # Try different trend and seasonal components
        for trend in ['add', 'mul', None]:
            for seasonal in ['add', 'mul', None]:
                try:
                    model = ExponentialSmoothing(
                        data_indexed['Volume'],
                        trend=trend,
                        seasonal=seasonal,
                        seasonal_periods=12 if seasonal else None,
                        initialization_method='estimated'
                    )

                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        model_fit = model.fit()

                        if model_fit.aic < best_aic:
                            best_aic = model_fit.aic
                            best_forecast = model_fit.forecast(periods)
                except:
                    continue

        # If model fails, use historical pattern-based forecast
        if best_forecast is None:
            # Use recent average with historical patterns
            base_forecast = patterns['recent_avg'] if patterns['recent_avg'] > 0 else patterns['historical_avg']
            best_forecast = np.full(periods, base_forecast)

        # Ensure ETS forecast is not unreasonably low compared to recent performance
        ets_avg = np.mean(best_forecast)
        recent_avg = patterns['recent_avg']
        if ets_avg < recent_avg * 0.5:  # ETS forecast is less than 50% of recent average
            # Blend ETS with recent average to prevent unrealistic drops
            blend_factor = 0.7  # 70% recent average, 30% ETS
            blended_forecast = recent_avg * blend_factor + ets_avg * (1 - blend_factor)
            adjustment_ratio = blended_forecast / ets_avg if ets_avg > 0 else 1.0
            best_forecast = best_forecast * adjustment_ratio

        # Create forecast dataframe
        last_date = data_indexed.index[-1]
        forecast_dates = pd.date_range(
            start=last_date + pd.DateOffset(months=1),
            periods=periods,
            freq='MS'
        )

        # Apply historical patterns to improve forecast accuracy
        forecast_df = pd.DataFrame({
            'Period': forecast_dates,
            'Month': forecast_dates.month,
            'Volume': best_forecast.values if hasattr(best_forecast, 'values') else best_forecast
        })

        # Apply seasonal adjustments based on historical patterns
        seasonal_factors = patterns['seasonal_factors']
        forecast_df = forecast_df.merge(
            seasonal_factors.rename('SeasonalFactor'),
            left_on='Month',
            right_index=True,
            how='left'
        )

        # Fill any missing seasonal factors with 1.0
        forecast_df['SeasonalFactor'] = forecast_df['SeasonalFactor'].fillna(1.0)

        # Apply seasonal adjustment (but cap the impact)
        forecast_df['SeasonalFactor'] = forecast_df['SeasonalFactor'].clip(0.5, 2.0)
        forecast_df['Volume'] = forecast_df['Volume'] * forecast_df['SeasonalFactor']

        # Apply growth trend (more balanced approach)
        yearly_growth = patterns['yearly_growth']
        trend_factor = patterns['trend_factor']
        recent_avg = patterns['recent_avg']

        # Cap yearly growth to prevent unrealistic projections
        capped_yearly_growth = max(-0.2, min(0.25, yearly_growth))
        growth_multiplier = (1 + capped_yearly_growth) ** (np.arange(periods) / 12 + 1)

        # Apply trend factor more conservatively
        adjusted_trend_factor = max(0.7, min(1.5, trend_factor))

        forecast_df['Volume'] = forecast_df['Volume'] * growth_multiplier * adjusted_trend_factor

        # Ensure forecast doesn't fall below a reasonable minimum
        min_forecast = recent_avg * 0.3
        forecast_df['Volume'] = forecast_df['Volume'].clip(lower=min_forecast)

        # Calculate NSV forecast with historical NSV per unit ratio
        avg_nsv_per_unit = (data['NSV'] / data['Volume']).replace([np.inf, -np.inf], np.nan).mean()
        if pd.isna(avg_nsv_per_unit) or avg_nsv_per_unit <= 0:
            avg_nsv_per_unit = data['NSV'].mean() / data['Volume'].mean() if data['Volume'].mean() > 0 else 1.0

        # Calculate NSV using current pricing (no price assumptions)
        forecast_df['NSV'] = forecast_df['Volume'] * avg_nsv_per_unit
        
        # Validate and adjust forecast against historical data
        forecast_df = validate_and_adjust_forecast(forecast_df, data)

        # Add metadata - include all required columns for complete forecast output
        metadata_cols = ['Product', 'Customer', 'Category', 'Description', 'SubCategory',
                        'Name', 'Region', 'Warehouse', 'WHDescription']
        for col in metadata_cols:
            if col in data.columns:
                forecast_df[col] = data[col].iloc[0]

        return forecast_df[['Period', 'Product', 'Customer', 'Volume', 'NSV', 'Category',
                          'Description', 'SubCategory', 'Name', 'Region', 'Warehouse', 'WHDescription']]

    except Exception as e:
        print(f"Error in enhanced_exponential_smoothing_forecast: {str(e)}")
        return enhanced_sarima_forecast(data, periods)  # Fallback to SARIMA

def validate_and_adjust_forecast(forecast_df, historical_data):
    """Validate forecast against historical data and adjust if necessary"""
    if forecast_df.empty or historical_data.empty:
        return forecast_df

    try:
        # Calculate historical statistics for Volume
        hist_avg = historical_data['Volume'].mean()
        hist_max = historical_data['Volume'].max()
        hist_min = historical_data['Volume'].min()
        hist_std = historical_data['Volume'].std()

        # Calculate historical statistics for NSV
        hist_nsv_avg = historical_data['NSV'].mean()
        hist_nsv_max = historical_data['NSV'].max()
        recent_nsv_data = historical_data.tail(6)
        recent_nsv_avg = recent_nsv_data['NSV'].mean() if len(recent_nsv_data) > 0 else hist_nsv_avg

        # Calculate recent trend (last 6 months vs previous 6 months)
        recent_data = historical_data.tail(6)
        recent_avg = recent_data['Volume'].mean() if len(recent_data) > 0 else hist_avg

        # Calculate growth trend if we have enough data
        growth_trend = 0
        if len(historical_data) >= 12:
            previous_6_months = historical_data.iloc[-12:-6]['Volume'].mean()
            if previous_6_months > 0:
                growth_trend = (recent_avg - previous_6_months) / previous_6_months

        # Check forecast reasonableness
        forecast_avg = forecast_df['Volume'].mean()
        forecast_max = forecast_df['Volume'].max()

        # Validation rules - more intelligent and less aggressive
        adjustments_made = []

        # Rule 1: Forecast shouldn't be more than 5x historical max (increased from 3x)
        if forecast_max > hist_max * 5:
            forecast_df['Volume'] = forecast_df['Volume'].clip(upper=hist_max * 3)
            adjustments_made.append("Capped extreme high values")

        # Rule 2: Forecast shouldn't be less than 5% of historical average (reduced from 10%)
        min_threshold = hist_avg * 0.05
        if forecast_df['Volume'].min() < min_threshold:
            forecast_df['Volume'] = forecast_df['Volume'].clip(lower=min_threshold)
            adjustments_made.append("Raised extremely low values")

        # Rule 3: Only adjust if forecast is dramatically different from recent trends
        # Consider growth trend - if growing, allow much more flexibility
        if growth_trend > 0.3:  # Very strong growth trend (>30%)
            min_recent_threshold = recent_avg * 0.2  # Allow forecasts down to 20% of recent
        elif growth_trend > 0.1:  # Strong growth trend (>10%)
            min_recent_threshold = recent_avg * 0.3  # Allow forecasts down to 30% of recent
        elif growth_trend > 0:  # Positive growth
            min_recent_threshold = recent_avg * 0.4  # Allow forecasts down to 40% of recent
        else:  # Declining or flat trend
            min_recent_threshold = recent_avg * 0.5  # Allow forecasts down to 50% of recent

        if forecast_avg < min_recent_threshold:
            # Less aggressive adjustment - only bring up to 60% of recent average
            adjustment_factor = (recent_avg * 0.6) / forecast_avg
            # Cap adjustment factor to prevent extreme changes
            adjustment_factor = min(adjustment_factor, 2.0)
            forecast_df['Volume'] = forecast_df['Volume'] * adjustment_factor
            adjustments_made.append("Adjusted low forecast to match recent trends")

        # Rule 4: Intelligent growth adjustment based on historical patterns
        # Only apply if forecast is unreasonably low compared to historical performance
        if growth_trend > 0.2:  # If there's strong growth, be much less aggressive
            min_hist_threshold = hist_avg * 0.4  # Allow forecasts down to 40% of historical
        elif growth_trend > 0.1:  # If there's positive growth, be less aggressive
            min_hist_threshold = hist_avg * 0.5  # Allow forecasts down to 50% of historical
        else:  # If declining or flat, still ensure reasonable minimum
            min_hist_threshold = hist_avg * 0.6  # Allow forecasts down to 60% of historical

        if forecast_avg < min_hist_threshold:
            # Less aggressive adjustment - only bring up to 70% of historical average
            adjustment_factor = (hist_avg * 0.7) / forecast_avg
            # Cap adjustment factor to prevent extreme changes
            adjustment_factor = min(adjustment_factor, 1.5)
            forecast_df['Volume'] = forecast_df['Volume'] * adjustment_factor
            adjustments_made.append("Applied minimum growth adjustment")

        # Recalculate NSV based on adjusted volumes
        if len(adjustments_made) > 0:
            avg_nsv_per_unit = (historical_data['NSV'] / historical_data['Volume']).replace([np.inf, -np.inf], np.nan).mean()
            if pd.isna(avg_nsv_per_unit) or avg_nsv_per_unit <= 0:
                avg_nsv_per_unit = historical_data['NSV'].mean() / historical_data['Volume'].mean() if historical_data['Volume'].mean() > 0 else 1.0

            # Recalculate NSV using current pricing (no price assumptions)
            forecast_df['NSV'] = forecast_df['Volume'] * avg_nsv_per_unit

        # NSV-specific validation rules
        forecast_nsv_avg = forecast_df['NSV'].mean()

        # Rule 5: NSV forecast shouldn't exceed reasonable growth from historical average
        max_reasonable_nsv = hist_nsv_avg * 1.3  # Max 30% increase from historical average
        if forecast_nsv_avg > max_reasonable_nsv:
            nsv_adjustment_factor = (hist_nsv_avg * 1.2) / forecast_nsv_avg  # Adjust to 20% increase
            forecast_df['NSV'] = forecast_df['NSV'] * nsv_adjustment_factor
            adjustments_made.append("Capped unrealistic NSV growth")

        # Rule 6: NSV forecast shouldn't be less than 70% of recent average
        min_reasonable_nsv = recent_nsv_avg * 0.7
        if forecast_nsv_avg < min_reasonable_nsv:
            nsv_adjustment_factor = (recent_nsv_avg * 0.85) / forecast_nsv_avg  # Adjust to 85% of recent
            forecast_df['NSV'] = forecast_df['NSV'] * nsv_adjustment_factor
            adjustments_made.append("Raised unrealistically low NSV")

        # Rule 7: Ensure NSV per unit stays within reasonable bounds
        forecast_nsv_per_unit = (forecast_df['NSV'] / forecast_df['Volume']).mean()
        hist_nsv_per_unit = (historical_data['NSV'] / historical_data['Volume']).mean()

        if forecast_nsv_per_unit > hist_nsv_per_unit * 1.05:  # More than 5% price increase
            # Use current pricing (no price assumptions)
            forecast_df['NSV'] = forecast_df['Volume'] * hist_nsv_per_unit
            adjustments_made.append("Reverted to current pricing")

        # Log adjustments if any were made
        if adjustments_made:
            print(f"Forecast adjustments applied: {', '.join(adjustments_made)}")

        return forecast_df

    except Exception as e:
        print(f"Error in forecast validation: {str(e)}")
        return forecast_df


def weighted_average_forecast(data, periods=12, weights=(0.5, 0.5), fallback_to_weighted=True):
    """
    Weighted average forecast combining SARIMA and Exponential Smoothing models.
    
    Args:
        data: Input data with 'Period', 'Volume', and 'NSV' columns
        periods: Number of periods to forecast
        weights: Tuple of (weight_for_sarima, weight_for_ets)
        fallback_to_weighted: If True, fall back to weighted average if SARIMA fails
    """
    try:
        # First try SARIMA
        try:
            sarima_forecast = enhanced_sarima_forecast(data, periods)
            if not sarima_forecast.empty:
                return sarima_forecast
        except Exception as e:
            print(f"SARIMA forecast failed: {str(e)}")
            if not fallback_to_weighted:
                return enhanced_exponential_smoothing_forecast(data, periods)
        
        # If SARIMA fails and fallback is allowed, try weighted average
        if fallback_to_weighted:
            print("Falling back to weighted average forecast")
            # Generate both forecasts
            ets_forecast = enhanced_exponential_smoothing_forecast(data, periods)
            
            # If we have SARIMA forecast (partial success), use it with ETS
            if 'sarima_forecast' in locals() and not sarima_forecast.empty:
                # Calculate weighted average of volumes
                weighted_volume = (sarima_forecast['Volume'] * weights[0] + 
                                 ets_forecast['Volume'] * weights[1])
                
                # Calculate weighted average of NSV
                weighted_nsv = (sarima_forecast['NSV'] * weights[0] + 
                              ets_forecast['NSV'] * weights[1])
                
                # Create result dataframe
                result = sarima_forecast.copy()
                result['Volume'] = weighted_volume
                result['NSV'] = weighted_nsv
                return result
            
            # If no SARIMA forecast, just return ETS
            return ets_forecast
        
        # If we get here and no forecast was returned, try ETS
        return enhanced_exponential_smoothing_forecast(data, periods)
        
    except Exception as e:
        print(f"Error in weighted_average_forecast: {str(e)}")
        # Final fallback to ETS
        return enhanced_exponential_smoothing_forecast(data, periods)

# Streamlit App Configuration
st.set_page_config("Sales Forecasting V2", layout="wide")
st.title("📊 Enhanced Sales Forecasting (V2)")

def load_data_from_file(file_path):
    """Helper function to load and preprocess data from a file"""
    try:
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:  # Excel file
            df = pd.read_excel(file_path)
        
        # Preprocess data
        df = preprocess_data(df)
        return df
    except Exception as e:
        st.error(f"Error loading file: {str(e)}")
        return pd.DataFrame()

# Sidebar
with st.sidebar:
    st.header("⚙️ Configuration")
    
    # Data Loading Section
    st.subheader("Data Loading")
    
    # Add radio button for data source selection
    data_source = st.radio(
        "Select Data Source:",
        ["Connect to SQL Server", "Use Local sales.csv", "Upload Custom File"],
        index=0  # Default to SQL Server
    )
    
    df = None

    if data_source == "Connect to SQL Server":
        # SQL Server connection section
        st.subheader("🗄️ SQL Server Configuration")

        # Default connection string
        default_connection = "DRIVER=SQL Server;SERVER=10.101.102.50;UID=syspro_report;Trusted_Connection=Yes;APP=2007 Microsoft Office system;WSID=PFLPOM0083;DATABASE=SysproCompanyPAR"

        # Connection string input
        connection_string = st.text_area(
            "Connection String:",
            value=default_connection,
            height=100,
            help="SQL Server connection string. Default is configured for your Syspro database."
        )

        # SQL file path
        sql_file_path = os.path.join("config", "sales.sql")

        col1, col2 = st.columns(2)

        with col1:
            # Test connection button
            if st.button("🔍 Test Connection", type="secondary"):
                with st.expander("🔍 Connection Test Results", expanded=True):
                    st.info("🔄 Testing SQL Server connection...")
                    success, message = test_sql_server_connection(connection_string)
                    if success:
                        st.success(f"✅ {message}")
                    else:
                        st.error(f"❌ {message}")

        with col2:
            # Load data button
            if st.button("📊 Load Data from SQL Server", type="primary"):
                # Create collapsible section for connection messages
                with st.expander("📡 Connection Status", expanded=True):
                    if os.path.exists(sql_file_path):
                        st.info("🔄 Loading SQL query...")
                        sql_query = load_sql_query(sql_file_path)
                        if sql_query:
                            st.info("🔄 Connecting to SQL Server and executing query...")
                            df = load_data_from_sql_server(connection_string, sql_query)
                            if not df.empty:
                                st.info("🔄 Preprocessing data...")
                                # Preprocess data
                                df = preprocess_data(df)
                                if not df.empty:
                                    st.session_state.raw_data = df
                                    st.success(f"✅ Successfully loaded {len(df)} records from SQL Server")
                                    st.success("✅ Data preprocessing completed")
                                else:
                                    st.error("❌ No valid data after preprocessing")
                            else:
                                st.error("❌ No data returned from SQL Server")
                        else:
                            st.error("❌ Failed to load SQL query")
                    else:
                        st.error(f"❌ SQL file not found: {sql_file_path}")

                # Show data preview (only if data was loaded successfully)
                if 'raw_data' in st.session_state and not st.session_state.raw_data.empty:
                    with st.expander("📋 Data Preview", expanded=False):
                                    st.write(f"**Columns:** {', '.join(df.columns)}")
                                    st.write(f"**Date Range:** {df['Period'].min()} to {df['Period'].max()}")
                                    st.write(f"**Products:** {df['Product'].nunique()} unique")
                                    st.write(f"**Customers:** {df['Customer'].nunique()} unique")

                                    # Show 2025 summary
                                    df_2025 = df[df['Period'].dt.year == 2025]
                                    if not df_2025.empty:
                                        st.write("**2025 Summary (Jan-Jul):**")
                                        total_2025_volume = df_2025['Volume'].sum()
                                        total_2025_nsv = df_2025['NSV'].sum()
                                        st.write(f"- **Volume:** {total_2025_volume:,.0f}")
                                        st.write(f"- **NSV:** {total_2025_nsv:,.0f} ({total_2025_nsv/1000000:.1f}M)")
                                        st.write(f"- **Records:** {len(df_2025):,}")

                                        # Show monthly breakdown
                                        monthly_2025 = df_2025.groupby(df_2025['Period'].dt.strftime('%Y-%m')).agg({
                                            'Volume': 'sum',
                                            'NSV': 'sum'
                                        }).reset_index()
                                        st.write("**Monthly Breakdown:**")
                                        for _, row in monthly_2025.iterrows():
                                            st.write(f"  - {row['Period']}: Volume={row['Volume']:,.0f}, NSV={row['NSV']:,.0f}")
                                    else:
                                        st.write("**⚠️ No 2025 data found in loaded dataset**")

                                    st.dataframe(df.head())

                # Show data quality report if available
                if 'data_quality_report' in st.session_state:
                    with st.expander("📊 Data Quality & Processing Report", expanded=False):
                        report = st.session_state.data_quality_report
                        st.markdown(f"""
                        ### 📈 **Processing Summary**
                        - **Original Records:** {report['original_records']:,}
                        - **Final Monthly Records:** {report['final_records']:,}
                        - **Unique Products:** {report['unique_products']:,}
                        - **Unique Customers:** {report['unique_customers']:,}
                        - **Product-Customer Combinations:** {report['unique_combinations_final']:,}
                        - **Warehouses:** {report['unique_warehouses']:,} (assigned based on historical patterns)

                        {report['date_info']}

                        {report['transaction_info']}

                        {report['quality_details']}

                        {report['duplicate_info']}

                        ### ✅ **Data Ready for Forecasting**
                        All data has been cleaned, validated, and aggregated to monthly buckets by Product-Customer combinations.
                        """)

        # Show SQL query preview
        with st.expander("📝 SQL Query Preview", expanded=False):
            if os.path.exists(sql_file_path):
                sql_content = load_sql_query(sql_file_path)
                if sql_content:
                    st.code(sql_content, language="sql")
                else:
                    st.error("❌ Failed to load SQL file")
            else:
                st.error(f"❌ SQL file not found: {sql_file_path}")
                st.info("💡 Make sure the 'config/sales.sql' file exists in your project directory")

    elif data_source == "Use Local sales.csv":
        # Try to load local sales.csv file
        local_file_path = os.path.join("data", "sales.csv")
        if os.path.exists(local_file_path):
            df = load_data_from_file(local_file_path)
            if not df.empty:
                st.session_state.raw_data = df
                st.success(f"✅ Successfully loaded {len(df)} records from local sales.csv")
        else:
            st.error("❌ Local sales.csv file not found in the data folder")

    else:  # Upload Custom File
        uploaded_file = st.file_uploader("Upload Sales Data (CSV/Excel)", type=['csv', 'xlsx', 'xls'])
        
        if uploaded_file is not None:
            if uploaded_file.name.endswith('.csv'):
                df = pd.read_csv(uploaded_file)
            else:  # Excel file
                df = pd.read_excel(uploaded_file)
            
            # Preprocess data
            df = preprocess_data(df)
            
            if not df.empty:
                st.session_state.raw_data = df
                st.success(f"✅ Successfully loaded {len(df)} records from uploaded file")
    
    # Forecast Settings
    if 'raw_data' in st.session_state:
        st.subheader("Forecast Settings")
        
        # Model Selection
        forecast_model = st.selectbox(
            "Forecast Model:",
            ["SARIMA", "Exponential Smoothing", "Weighted Average (SARIMA + ETS)"],
            index=0
        )
        
        # Show weights slider if Weighted Average is selected
        if forecast_model == "Weighted Average (SARIMA + ETS)":
            sarima_weight = st.slider(
                "SARIMA Weight",
                min_value=0.0,
                max_value=1.0,
                value=0.5,
                step=0.1,
                help="Weight for SARIMA model (remaining weight will be applied to Exponential Smoothing)"
            )
        
        # Forecast Horizon
        forecast_months = st.slider(
            "Forecast Horizon (months):",
            min_value=1,
            max_value=36,
            value=12,
            step=1
        )
        
        # Advanced settings in an expander
        with st.expander("⚙️ Advanced Settings", expanded=False):
            # Calculate coverage statistics
            if 'raw_data' in st.session_state:
                df = st.session_state.raw_data
                combi_counts = df.groupby(['Product', 'Customer']).size().reset_index(name='DataPoints')
                total_combinations = len(combi_counts)

                # Show coverage for different minimums
                st.write("**📊 Forecast Coverage Analysis:**")
                coverage_options = [6, 9, 12, 15, 18, 24]
                coverage_data = []

                for min_req in coverage_options:
                    valid_count = len(combi_counts[combi_counts['DataPoints'] >= min_req])
                    coverage_pct = valid_count / total_combinations * 100
                    coverage_data.append(f"{min_req} months: {valid_count:,} combinations ({coverage_pct:.1f}%)")

                for coverage_info in coverage_data:
                    st.write(f"- {coverage_info}")

            # Smart minimum data requirement
            st.write("**🎯 Minimum Data Requirement:**")

            # Offer different strategies
            strategy = st.radio(
                "Forecasting Strategy:",
                [
                    "Full History (Use all available data)",
                    "Balanced Coverage (12 months minimum)",
                    "High Quality (18 months minimum)",
                    "Maximum Coverage (6 months minimum)",
                    "Custom Minimum"
                ],
                index=0,  # Default to Full History
                help="Choose between forecast coverage and quality"
            )

            if strategy == "Full History (Use all available data)":
                min_data_points = 1  # Use any combination with at least 1 data point
                st.success("🌟 **Full History** - Uses all available data for maximum insights and 100% coverage")
                st.info("💡 **Benefits**: Leverages complete historical patterns, seasonal trends, and long-term growth")
            elif strategy == "Balanced Coverage (12 months minimum)":
                min_data_points = 12
                st.info("✅ **Balanced approach** - Good quality with ~18% coverage")
            elif strategy == "High Quality (18 months minimum)":
                min_data_points = 18
                st.warning("⚠️ **High quality but low coverage** - Only ~3% of combinations")
            elif strategy == "Maximum Coverage (6 months minimum)":
                min_data_points = 6
                st.info("📈 **Maximum coverage** - ~46% of combinations (lower quality)")
            else:  # Custom
                recommended_min = max(12, forecast_months * 2)  # More reasonable default
                min_data_points = st.slider(
                    "Custom Minimum Data Points (months)",
                    min_value=6,
                    max_value=36,
                    value=recommended_min,
                    step=1,
                    help="Custom minimum months of historical data required"
                )

                # Show impact of custom selection
                if 'raw_data' in st.session_state:
                    valid_count = len(combi_counts[combi_counts['DataPoints'] >= min_data_points])
                    coverage_pct = valid_count / total_combinations * 100
                    st.write(f"**Impact:** {valid_count:,} combinations ({coverage_pct:.1f}% coverage)")

            # Show quality guidance
            if min_data_points == 1:
                st.info("🌟 **Full History Mode**: Uses all available data - quality varies by product-customer combination")
            elif min_data_points < 12:
                st.warning("⚠️ Very low data requirement - forecasts may be unreliable")
            elif min_data_points >= 24:
                st.success("✅ Excellent data requirement - high quality forecasts")

            # Fallback forecasting option
            st.write("**🔄 Fallback Forecasting:**")
            include_fallback = st.checkbox(
                "Include simple forecasts for excluded combinations",
                value=True,
                help="Generate basic forecasts for combinations with insufficient data using recent averages"
            )

            if include_fallback:
                st.info("✅ Will generate simple forecasts for excluded combinations using recent average method")
        
        # Generate Forecasts button
        if st.button("🚀 Generate Forecasts", type="primary"):
            df = st.session_state.raw_data
            
            # Find valid product-customer combinations
            combi_counts = df.groupby(['Product', 'Customer']).size().reset_index(name='DataPoints')
            valid_combi = combi_counts[combi_counts['DataPoints'] >= min_data_points]
            excluded_combi = combi_counts[combi_counts['DataPoints'] < min_data_points]

            # Show coverage statistics
            total_combinations = len(combi_counts)
            valid_count = len(valid_combi)
            excluded_count = len(excluded_combi)

            st.info(f"""
            **📊 Forecast Coverage:**
            - ✅ **Valid combinations:** {valid_count:,} ({valid_count/total_combinations*100:.1f}%)
            - ❌ **Excluded combinations:** {excluded_count:,} ({excluded_count/total_combinations*100:.1f}%)
            - 📋 **Minimum requirement:** {min_data_points} months
            """)

            if valid_combi.empty:
                st.error("❌ No product-customer combinations meet the minimum data requirements")
                st.write("💡 **Suggestions:**")
                st.write("- Reduce the minimum data requirement")
                st.write("- Use 'Maximum Coverage' strategy")
                st.write("- Check if data is loaded correctly")
            else:
                # Initialize progress bar and status text
                progress_bar = st.progress(0)
                status_text = st.empty()
                total_combinations = len(valid_combi)
                
                results = []
                successful = 0
                failed = 0
                
                try:
                    for idx, (_, row) in enumerate(valid_combi.iterrows()):
                        # Update progress - ensure it stays within [0.0, 1.0]
                        progress = min(1.0, (idx + 1) / total_combinations)
                        progress_bar.progress(progress)
                        status_text.text(f"🔄 Processing {idx + 1} of {total_combinations}: {row['Product']} - {row['Customer']}...")
                        
                        try:
                            # Filter data for current combination
                            combi_data = df[
                                (df['Product'] == row['Product']) & 
                                (df['Customer'] == row['Customer'])
                            ].sort_values('Period')
                            
                            # Generate forecast based on selected model
                            if forecast_model == "SARIMA":
                                # For SARIMA, try it first and fall back to weighted if needed
                                try:
                                    forecast = enhanced_sarima_forecast(combi_data, forecast_months)
                                    if forecast.empty:
                                        raise Exception("SARIMA returned empty forecast")
                                except Exception as e:
                                    print(f"SARIMA failed, falling back to weighted average: {str(e)}")
                                    forecast = weighted_average_forecast(
                                        combi_data,
                                        forecast_months,
                                        weights=(0.7, 0.3),  # Slightly favor SARIMA if it partially works
                                        fallback_to_weighted=True
                                    )
                            elif forecast_model == "Exponential Smoothing":
                                forecast = enhanced_exponential_smoothing_forecast(combi_data, forecast_months)
                            else:  # Weighted Average
                                forecast = weighted_average_forecast(
                                    combi_data, 
                                    forecast_months, 
                                    weights=(sarima_weight, 1 - sarima_weight),
                                    fallback_to_weighted=True
                                )
                            
                            if not forecast.empty:
                                results.append(forecast)
                                successful += 1
                            else:
                                failed += 1
                                
                        except Exception as e:
                            print(f"Error processing {row['Product']} - {row['Customer']}: {str(e)}")
                            failed += 1
                finally:
                    # Clear the progress bar and status text when done
                    progress_bar.empty()
                    status_text.empty()

                    # Add fallback forecasting for excluded combinations
                    if include_fallback and not excluded_combi.empty:
                        st.info(f"🔄 Generating fallback forecasts for {len(excluded_combi)} excluded combinations...")

                        fallback_progress = st.progress(0)
                        fallback_status = st.empty()

                        try:
                            fallback_count = 0
                            total_fallback = len(excluded_combi)

                            for idx, row in excluded_combi.iterrows():
                                fallback_count += 1
                                # Ensure progress value stays within [0.0, 1.0]
                                progress_value = min(1.0, fallback_count / total_fallback)
                                fallback_progress.progress(progress_value)
                                fallback_status.text(f"Fallback forecast {fallback_count}/{total_fallback}: {row['Product']} - {row['Customer']}")

                                try:
                                    # Get data for this combination
                                    combi_data = df[(df['Product'] == row['Product']) & (df['Customer'] == row['Customer'])].copy()
                                    combi_data = combi_data.sort_values('Period')

                                    if len(combi_data) >= 3:  # Need at least 3 months for fallback
                                        # Simple fallback: use recent average
                                        recent_months = min(6, len(combi_data))
                                        recent_avg_volume = combi_data.tail(recent_months)['Volume'].mean()
                                        recent_avg_nsv = combi_data.tail(recent_months)['NSV'].mean()

                                        # Create simple forecast
                                        last_period = combi_data['Period'].max()
                                        future_periods = pd.date_range(
                                            start=last_period + pd.DateOffset(months=1),
                                            periods=forecast_months,
                                            freq='MS'
                                        )

                                        fallback_forecast = pd.DataFrame({
                                            'Period': future_periods,
                                            'Product': row['Product'],
                                            'Customer': row['Customer'],
                                            'Volume': recent_avg_volume,
                                            'NSV': recent_avg_nsv,
                                            'Category': combi_data['Category'].iloc[0] if 'Category' in combi_data.columns else 'Unknown',
                                            'Description': combi_data['Description'].iloc[0] if 'Description' in combi_data.columns else 'Unknown',
                                            'SubCategory': combi_data['SubCategory'].iloc[0] if 'SubCategory' in combi_data.columns else 'Unknown',
                                            'Name': combi_data['Name'].iloc[0] if 'Name' in combi_data.columns else 'Unknown',
                                            'Region': combi_data['Region'].iloc[0] if 'Region' in combi_data.columns else 'Unknown',
                                            'Warehouse': combi_data['Warehouse'].iloc[0] if 'Warehouse' in combi_data.columns else 'Unknown',
                                            'WHDescription': combi_data['WHDescription'].iloc[0] if 'WHDescription' in combi_data.columns else 'Unknown',
                                            'ForecastMethod': 'Fallback (Recent Average)',
                                            'DataPoints': len(combi_data)
                                        })

                                        results.append(fallback_forecast)
                                        successful += 1

                                except Exception as e:
                                    print(f"Error in fallback forecast for {row['Product']} - {row['Customer']}: {str(e)}")
                                    failed += 1
                        finally:
                            fallback_progress.empty()
                            fallback_status.empty()

                    if results:
                        # Combine all forecasts
                        final_forecast = pd.concat(results, ignore_index=True)
                        final_forecast['Year'] = final_forecast['Period'].dt.year
                        final_forecast['Month'] = final_forecast['Period'].dt.strftime('%b %Y')
                        
                        # Add forecast flag and prepare for merging with actuals
                        final_forecast['is_forecast'] = True
                        
                        # Get actuals data for the same product-customer combinations
                        actuals = st.session_state.raw_data.copy()
                        actuals = actuals[actuals[['Product', 'Customer']].apply(tuple, 1).isin(
                            final_forecast[['Product', 'Customer']].apply(tuple, 1)
                        )]
                        
                        if not actuals.empty:
                            # Find the latest actual date for each product-customer combination
                            latest_dates = actuals.groupby(['Product', 'Customer'])['Period'].max().reset_index()
                            
                            # Filter forecasts to start from the month after the latest actual date for each combination
                            filtered_forecasts = []
                            for _, row in latest_dates.iterrows():
                                product = row['Product']
                                customer = row['Customer']
                                last_actual_date = row['Period']
                                
                                # Get forecasts for this product-customer combination
                                forecast_subset = final_forecast[
                                    (final_forecast['Product'] == product) & 
                                    (final_forecast['Customer'] == customer) &
                                    (final_forecast['Period'] > last_actual_date)
                                ]
                                filtered_forecasts.append(forecast_subset)
                            
                            # Combine all filtered forecasts
                            if filtered_forecasts:
                                final_forecast = pd.concat(filtered_forecasts, ignore_index=True)
                            
                            actuals['is_forecast'] = False
                            # Ensure columns match between actuals and forecast
                            actuals = actuals[final_forecast.columns.intersection(actuals.columns)]
                            
                            # Combine actuals and filtered forecasts
                            combined_data = pd.concat([actuals, final_forecast], ignore_index=True)
                            combined_data.sort_values(['Product', 'Customer', 'Period'], inplace=True)
                            st.session_state.combined_data = combined_data
                        
                        st.session_state.forecast_data = final_forecast
                        
                        # Calculate coverage statistics
                        total_forecasted = successful
                        total_possible = len(combi_counts)
                        coverage_pct = total_forecasted / total_possible * 100

                        # Count fallback forecasts
                        fallback_count = len([r for r in results if 'ForecastMethod' in r.columns and 'Fallback' in r['ForecastMethod'].iloc[0]])
                        advanced_count = total_forecasted - fallback_count

                        st.success(f"""
                        ✅ **Forecast Generation Complete!**
                        - **Total forecasts:** {total_forecasted:,} combinations ({coverage_pct:.1f}% coverage)
                        - **Advanced forecasts:** {advanced_count:,} (SARIMA/ETS models)
                        - **Fallback forecasts:** {fallback_count:,} (Recent average method)
                        """ + (f"\n- **Failed:** {failed}" if failed > 0 else ""))
                        
                        # Show forecast summary
                        st.subheader("📊 Forecast Summary")
                        st.write(f"- Forecast Period: {final_forecast['Period'].min().strftime('%b %Y')} to {final_forecast['Period'].max().strftime('%b %Y')}")
                        st.write(f"- Products: {final_forecast['Product'].nunique()}")
                        st.write(f"- Customers: {final_forecast['Customer'].nunique()}")
                        st.info("💰 **Pricing**: Forecasts use current pricing levels (no price increase assumptions)")
                        
                        # Calculate total forecast
                        total_volume = final_forecast['Volume'].sum()
                        total_nsv = final_forecast['NSV'].sum()
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("Total Forecast Volume", format_number(total_volume))
                        with col2:
                            st.metric("Total Forecast NSV", format_currency(total_nsv))

                        # Add NSV validation information
                        if 'raw_data' in st.session_state:
                            historical_data = st.session_state.raw_data

                            # Calculate historical monthly averages
                            historical_monthly = historical_data.groupby('Period').agg({
                                'Volume': 'sum',
                                'NSV': 'sum'
                            }).reset_index()

                            hist_monthly_nsv = historical_monthly['NSV'].mean()
                            hist_monthly_volume = historical_monthly['Volume'].mean()

                            # Calculate forecast monthly averages
                            forecast_months_count = final_forecast['Period'].nunique()
                            forecast_monthly_nsv = total_nsv / forecast_months_count
                            forecast_monthly_volume = total_volume / forecast_months_count

                            # Show comparison
                            st.write("**🔍 Forecast Validation:**")

                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.metric(
                                    "Historical Avg Monthly NSV",
                                    format_currency(hist_monthly_nsv),
                                    help="Average monthly NSV from historical data"
                                )
                            with col2:
                                nsv_change = (forecast_monthly_nsv - hist_monthly_nsv) / hist_monthly_nsv * 100
                                st.metric(
                                    "Forecast Avg Monthly NSV",
                                    format_currency(forecast_monthly_nsv),
                                    delta=f"{nsv_change:+.1f}%",
                                    help="Average monthly NSV from forecast"
                                )
                            with col3:
                                # Validation status
                                if abs(nsv_change) <= 30:
                                    st.success("✅ Realistic forecast")
                                elif abs(nsv_change) <= 50:
                                    st.warning("⚠️ High growth forecast")
                                else:
                                    st.error("🚨 Unrealistic forecast")

                            # Additional insights
                            if nsv_change > 50:
                                st.warning(f"""
                                ⚠️ **High Growth Alert**: Forecast shows {nsv_change:.1f}% increase in monthly NSV.
                                This indicates optimistic volume projections (using current pricing).
                                """)
                            elif nsv_change < -30:
                                st.info(f"""
                                📉 **Decline Alert**: Forecast shows {nsv_change:.1f}% decrease in monthly NSV.
                                This may reflect market challenges or conservative projections.
                                """)

                        # Add Category Monthly Splits Summary
                        st.subheader("📊 Category Monthly Splits")

                        # Calculate category monthly splits
                        category_monthly = final_forecast.groupby(['Category', final_forecast['Period'].dt.strftime('%Y-%m')]).agg({
                            'Volume': 'sum',
                            'NSV': 'sum'
                        }).reset_index()
                        category_monthly.columns = ['Category', 'Month', 'Volume', 'NSV']

                        # Create pivot tables for better display
                        volume_pivot = category_monthly.pivot(index='Category', columns='Month', values='Volume').fillna(0)
                        nsv_pivot = category_monthly.pivot(index='Category', columns='Month', values='NSV').fillna(0)

                        # Display volume splits
                        st.write("**📦 Volume by Category and Month:**")

                        # Format volume data for display
                        volume_display = volume_pivot.copy()
                        for col in volume_display.columns:
                            volume_display[col] = volume_display[col].apply(lambda x: f"{x:,.0f}" if x > 0 else "-")

                        # Add totals
                        volume_display['Total'] = volume_pivot.sum(axis=1).apply(lambda x: f"{x:,.0f}")

                        st.dataframe(volume_display, use_container_width=True)

                        # Display NSV splits
                        st.write("**💰 NSV by Category and Month:**")

                        # Format NSV data for display
                        nsv_display = nsv_pivot.copy()
                        for col in nsv_display.columns:
                            nsv_display[col] = nsv_display[col].apply(lambda x: f"{x:,.0f}" if x > 0 else "-")

                        # Add totals
                        nsv_display['Total'] = nsv_pivot.sum(axis=1).apply(lambda x: f"{x:,.0f}")

                        st.dataframe(nsv_display, use_container_width=True)

                        # Summary statistics
                        col1, col2 = st.columns(2)

                        with col1:
                            st.write("**📈 Category Volume Summary:**")
                            category_volume_totals = volume_pivot.sum(axis=1).sort_values(ascending=False)
                            total_volume_all = category_volume_totals.sum()

                            for category, volume in category_volume_totals.items():
                                percentage = (volume / total_volume_all * 100) if total_volume_all > 0 else 0
                                st.write(f"- **{category}**: {volume:,.0f} ({percentage:.1f}%)")

                        with col2:
                            st.write("**💰 Category NSV Summary:**")
                            category_nsv_totals = nsv_pivot.sum(axis=1).sort_values(ascending=False)
                            total_nsv_all = category_nsv_totals.sum()

                            for category, nsv in category_nsv_totals.items():
                                percentage = (nsv / total_nsv_all * 100) if total_nsv_all > 0 else 0
                                st.write(f"- **{category}**: {nsv:,.0f} ({percentage:.1f}%)")

                        # Monthly totals
                        st.write("**📅 Monthly Totals Across All Categories:**")
                        monthly_totals = final_forecast.groupby(final_forecast['Period'].dt.strftime('%Y-%m')).agg({
                            'Volume': 'sum',
                            'NSV': 'sum'
                        }).reset_index()
                        monthly_totals.columns = ['Month', 'Volume', 'NSV']

                        # Format for display
                        monthly_display = monthly_totals.copy()
                        monthly_display['Volume'] = monthly_display['Volume'].apply(lambda x: f"{x:,.0f}")
                        monthly_display['NSV'] = monthly_display['NSV'].apply(lambda x: f"{x:,.0f}")

                        st.dataframe(monthly_display, use_container_width=True, hide_index=True)

                        # Add visual charts for category splits
                        st.write("**📊 Visual Category Analysis:**")

                        col1, col2 = st.columns(2)

                        with col1:
                            # Category Volume Pie Chart

                            category_volume_df = pd.DataFrame({
                                'Category': category_volume_totals.index,
                                'Volume': category_volume_totals.values
                            })

                            fig_volume = px.pie(
                                category_volume_df,
                                values='Volume',
                                names='Category',
                                title='Volume Distribution by Category',
                                color_discrete_sequence=px.colors.qualitative.Set3
                            )
                            fig_volume.update_traces(textposition='inside', textinfo='percent+label')
                            fig_volume.update_layout(height=400)
                            st.plotly_chart(fig_volume, use_container_width=True)

                        with col2:
                            # Category NSV Pie Chart
                            category_nsv_df = pd.DataFrame({
                                'Category': category_nsv_totals.index,
                                'NSV': category_nsv_totals.values
                            })

                            fig_nsv = px.pie(
                                category_nsv_df,
                                values='NSV',
                                names='Category',
                                title='NSV Distribution by Category',
                                color_discrete_sequence=px.colors.qualitative.Set2
                            )
                            fig_nsv.update_traces(textposition='inside', textinfo='percent+label')
                            fig_nsv.update_layout(height=400)
                            st.plotly_chart(fig_nsv, use_container_width=True)

                        # Monthly trend chart
                        st.write("**📈 Monthly Forecast Trends by Category:**")

                        # Prepare data for monthly trend
                        monthly_trend_data = final_forecast.groupby(['Category', final_forecast['Period'].dt.strftime('%Y-%m')]).agg({
                            'Volume': 'sum',
                            'NSV': 'sum'
                        }).reset_index()
                        monthly_trend_data.columns = ['Category', 'Month', 'Volume', 'NSV']

                        # Create monthly trend charts
                        col1, col2 = st.columns(2)

                        with col1:
                            fig_trend_vol = px.line(
                                monthly_trend_data,
                                x='Month',
                                y='Volume',
                                color='Category',
                                title='Monthly Volume Trends by Category',
                                markers=True
                            )
                            fig_trend_vol.update_layout(height=400)
                            st.plotly_chart(fig_trend_vol, use_container_width=True)

                        with col2:
                            fig_trend_nsv = px.line(
                                monthly_trend_data,
                                x='Month',
                                y='NSV',
                                color='Category',
                                title='Monthly NSV Trends by Category',
                                markers=True
                            )
                            fig_trend_nsv.update_layout(height=400)
                            st.plotly_chart(fig_trend_nsv, use_container_width=True)

                    else:
                        st.error("❌ No forecasts could be generated")



# Transaction History Section
if 'preserved_transactions' in st.session_state and not st.session_state.preserved_transactions.empty:
    with st.sidebar:
        st.subheader("📋 Transaction History")

        preserved_data = st.session_state.preserved_transactions
        total_transactions = len(preserved_data)
        unique_months = preserved_data['Period'].nunique()

        st.write(f"**Preserved Transactions:** {total_transactions:,}")
        st.write(f"**Unique Months:** {unique_months:,}")

        # Show sample of preserved transactions
        with st.expander("View Transaction Details"):
            # Filter for transactions with duplicates
            duplicate_mask = preserved_data.duplicated(subset=['Product', 'Customer', 'Period'], keep=False)
            duplicate_transactions = preserved_data[duplicate_mask]

            if not duplicate_transactions.empty:
                st.write(f"**Sample Duplicate Transactions:** {len(duplicate_transactions):,} total")
                sample_duplicates = duplicate_transactions.head(10)[
                    ['Product', 'Customer', 'Period', 'TransactionSeq', 'Volume', 'NSV']
                ]
                st.dataframe(sample_duplicates)
            else:
                st.write("No duplicate transactions found in preserved data")

        # Download preserved transactions
        if st.button("📥 Download Full Transaction History", type="secondary"):
            # Prepare preserved data for download
            download_preserved = prepare_forecast_for_download(preserved_data)

            if not download_preserved.empty:
                # Add transaction sequence to download
                if 'TransactionSeq' in preserved_data.columns:
                    download_preserved['TransactionSeq'] = preserved_data['TransactionSeq']

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"transaction_history_{timestamp}.csv"

                csv_data = download_preserved.to_csv(index=False)

                st.download_button(
                    label="📥 Download Transaction History CSV",
                    data=csv_data.encode('utf-8'),
                    file_name=filename,
                    mime="text/csv"
                )

# Main Content Area
if 'combined_data' in st.session_state and not st.session_state.combined_data.empty:
    df = st.session_state.combined_data
    title = "📊 Actuals + Forecast Dashboard"
    trend_title = "📈 Actuals vs Forecast Trend"
    data_title = "📋 Combined Data"
    is_forecast = True
    show_actuals = True
elif 'forecast_data' in st.session_state and st.session_state.forecast_data is not None:
    df = st.session_state.forecast_data
    title = "📊 Forecast Dashboard"
    trend_title = "📈 Forecast Trend"
    data_title = "📋 Forecast Data"
    is_forecast = True
    show_actuals = False
elif 'raw_data' in st.session_state:
    df = st.session_state.raw_data
    title = "📊 Data Overview"
    trend_title = "📈 Data Trend"
    data_title = "📋 Detailed Data"
    is_forecast = False
    show_actuals = False
else:
    st.subheader("🚀 Welcome to Sales Forecasting (V2)")
    st.write("""
    This app provides enhanced sales forecasting capabilities with improved trend preservation.
    
    **Features:**
    - Advanced SARIMA and Exponential Smoothing models
    - Automatic parameter selection
    - Seasonality and trend detection
    - Product-customer level forecasting
    - Interactive visualizations
    
    **Getting Started:**
    1. Upload your sales data (CSV or Excel)
    2. Configure forecast settings
    3. Generate and analyze forecasts
    """)
    st.info("ℹ️ Please upload your data using the sidebar to get started.")
    st.stop()

# Create filter section
st.subheader("📊 Sales Data Analysis & Forecasting")

# Add view mode selector if we have combined data
if 'combined_data' in st.session_state and not st.session_state.combined_data.empty:
    view_mode = st.radio(
        "View Mode:",
        ["All", "Actuals Only", "Forecasts Only"],
        horizontal=True,
        index=0
    )

    # Filter data based on view mode
    if view_mode == "Actuals Only":
        df = df[~df['is_forecast']]
    elif view_mode == "Forecasts Only":
        df = df[df['is_forecast']]
    # For "All" view, use all data

# Get unique values for filters
years = sorted(df['Period'].dt.year.unique(), reverse=True)
categories = sorted(df['Category'].unique())
products = sorted(df['Product'].unique())

# Get current year
current_year = pd.Timestamp.now().year

# Create filters
col1, col2, col3 = st.columns(3)

with col1:
    # Set default to current year if available, otherwise first year in the list
    default_year_idx = years.index(current_year) if current_year in years else 0
    selected_year = st.selectbox("Select Year:", years, index=default_year_idx)

with col2:
    selected_categories = st.multiselect(
        "Filter by Category:",
        categories,
        default=[]
    )

with col3:
    selected_products = st.multiselect(
        "Filter by Product:",
        products,
        default=[]
    )

# Apply filters
filtered_data = df.copy()  # Start with a copy to preserve original data

# Apply year filter first
if selected_year:
    before_filter = len(filtered_data)
    filtered_data = filtered_data[filtered_data['Period'].dt.year == selected_year]
    after_filter = len(filtered_data)

    # Debug information (can be removed later)
    if selected_year == 2025:
        st.info(f"🔍 Year Filter Debug: Selected {selected_year}, Records before: {before_filter:,}, after: {after_filter:,}")

# Apply category filter
if selected_categories:
    filtered_data = filtered_data[filtered_data['Category'].isin(selected_categories)]

# Apply product filter
if selected_products:
    filtered_data = filtered_data[filtered_data['Product'].isin(selected_products)]

# Display summary metrics
if not filtered_data.empty:
    # Aggregate data by month first
    monthly_data = filtered_data.groupby('Period').agg({
        'Volume': 'sum',
        'NSV': 'sum'
    }).reset_index()

    # Calculate metrics from monthly aggregated data
    total_volume = monthly_data['Volume'].sum()
    total_nsv = monthly_data['NSV'].sum()
    avg_monthly_volume = monthly_data['Volume'].mean()
    avg_monthly_nsv = monthly_data['NSV'].mean()

    # Display metrics in two columns
    col1, col2 = st.columns(2)

    with col1:
        metric_title = "Total Forecast Volume" if is_forecast else "Total Volume"
        st.metric(metric_title, format_number(total_volume))
        st.metric("Avg Monthly Volume", format_number(avg_monthly_volume))

    with col2:
        metric_title = "Total Forecast NSV" if is_forecast else "Total NSV"
        st.metric(metric_title, format_currency(total_nsv))
        st.metric("Avg Monthly NSV", format_currency(avg_monthly_nsv))

    # Create time series chart
    st.subheader(trend_title)

# Create figure with secondary y-axis
fig = make_subplots(specs=[[{"secondary_y": True}]])

# If we have both actuals and forecasts
if 'combined_data' in st.session_state and not st.session_state.combined_data.empty:
    # Separate actuals and forecasts
    actuals = st.session_state.combined_data[~st.session_state.combined_data['is_forecast']]
    forecasts = st.session_state.combined_data[st.session_state.combined_data['is_forecast']]

    # Group by period for the chart
    actuals_monthly = actuals.groupby('Period').agg({'Volume': 'sum', 'NSV': 'sum'}).reset_index()
    forecasts_monthly = forecasts.groupby('Period').agg({'Volume': 'sum', 'NSV': 'sum'}).reset_index()

    # Add actuals traces
    fig.add_trace(
        go.Bar(
            x=actuals_monthly['Period'],
            y=actuals_monthly['Volume'],
            name='Actual Volume',
            marker_color='#1f77b4',
            opacity=0.7
        ),
        secondary_y=False,
    )

    # Add actual NSV trace
    fig.add_trace(
        go.Scatter(
            x=actuals_monthly['Period'],
            y=actuals_monthly['NSV'],
            name='Actual NSV',
            line=dict(color='#ff7f0e', width=2, dash='solid'),
            opacity=0.7
        ),
        secondary_y=True,
    )
        
        # Add a vertical line to separate actuals from forecasts
        if not forecasts_monthly.empty and not actuals_monthly.empty:
            last_actual_date = actuals_monthly['Period'].max()
            # Add a vertical line using a shape
            fig.add_shape(
                type="line",
                x0=last_actual_date,
                y0=0,
                x1=last_actual_date,
                y1=1,
                yref="paper",
                line=dict(
                    color="red",
                    width=2,
                    dash="dash"
                )
            )
            # Add annotation
            fig.add_annotation(
                x=last_actual_date,
                y=1.05,
                yref="paper",
                text="Forecast Start",
                showarrow=False,
                font=dict(color="red")
            )      
        # Add forecast traces with dashed lines
        fig.add_trace(
            go.Bar(
                x=forecasts_monthly['Period'],
                y=forecasts_monthly['Volume'],
                name='Forecast Volume',
                marker_color='#1f77b4',
                opacity=0.4
            ),
            secondary_y=False,
        )
        fig.add_trace(
            go.Scatter(
                x=forecasts_monthly['Period'],
                y=forecasts_monthly['NSV'],
                name='Forecast NSV',
                line=dict(color='#ff7f0e', width=2)
            ),
            secondary_y=True
        )

        # Set y-axes titles
        fig.update_yaxes(title_text="Volume", secondary_y=False)
        fig.update_yaxes(title_text="NSV (PGK)", secondary_y=True)

        # Update layout
        fig.update_layout(
            title=f"{trend_title} - {selected_year}",
            xaxis_title="Period",
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
            height=500
        )

    # Show the chart
    st.plotly_chart(fig, use_container_width=True)
else:
    # Fallback to simple chart if no actuals/forecasts separation needed
    try:
        # Create figure with secondary y-axis
        fig = make_subplots(specs=[[{"secondary_y": True}]])

        # Add traces
        fig.add_trace(
                go.Bar(
                    x=monthly_data['Period'],
                    y=monthly_data['Volume'],
                    name='Volume',
                    marker_color='#1f77b4'
                ),
                secondary_y=False
            )

        fig.add_trace(
            go.Scatter(
                x=monthly_data['Period'],
                y=monthly_data['NSV'],
                name='NSV',
                line=dict(color='#ff7f0e', width=2)
            ),
            secondary_y=True
        )

        # Set y-axes titles
        fig.update_yaxes(title_text="Volume", secondary_y=False)
        fig.update_yaxes(title_text="NSV (PGK)", secondary_y=True)

        # Update layout
        fig.update_layout(
            title=f"{trend_title} - {selected_year}",
            xaxis_title="Period",
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
            height=500
        )

        st.plotly_chart(fig, use_container_width=True)
        except Exception as e:
            st.error(f"Error creating chart: {str(e)}")
    
    if filtered_data.empty:
        st.warning("No data available for the selected filters")
    
    # Group data by Category and Period
    category_data = filtered_data.groupby(['Category', 'Period']).agg({
        'Volume': 'sum',
        'NSV': 'sum'
    }).reset_index()
    
    def calculate_category_summary(df, is_forecast=False):
        """Calculate summary metrics by category with year-over-year comparisons."""
        if df.empty:
            return pd.DataFrame()
        
        # Get current year and month range
        current_year = df['Period'].dt.year.max()
        current_months = df['Period'].dt.month.unique()
        
        # Filter for current and previous year data
        current_year_data = df[df['Period'].dt.year == current_year]
        previous_year_data = df[df['Period'].dt.year == current_year - 1]
        
        # If no previous year data, create empty DataFrame with same structure
        if previous_year_data.empty:
            previous_year_data = current_year_data.copy()
            previous_year_data['Volume'] = 0
            previous_year_data['NSV'] = 0
    
        # Group by Category and calculate metrics for current year
        current_summary = current_year_data.groupby('Category').agg({
            'Volume': ['sum', 'mean'],
            'NSV': ['sum', 'mean']
        }).reset_index()
        
        # Group by Category and calculate metrics for previous year
        previous_summary = previous_year_data.groupby('Category').agg({
            'Volume': ['sum', 'mean'],
            'NSV': ['sum', 'mean']
        }).reset_index()
        
        # Flatten multi-level columns before merge, but preserve 'Category' column name
        current_summary.columns = [col[0] if col[1] == '' else '_'.join(col).strip() 
                                for col in current_summary.columns.values]
        previous_summary.columns = [col[0] if col[1] == '' else '_'.join(col).strip() 
                                 for col in previous_summary.columns.values]
        
        # Ensure 'Category' column exists in both dataframes
        if 'Category' not in current_summary.columns:
            current_summary = current_summary.rename(columns={'Category_': 'Category'})
        if 'Category' not in previous_summary.columns:
            previous_summary = previous_summary.rename(columns={'Category_': 'Category'})
            
        # Merge summaries
        summary = pd.merge(current_summary, previous_summary, on='Category', how='left')
        
        # Calculate YoY changes with division by zero protection
        summary['Volume_YoY_Change'] = 0.0
        mask = (summary['Volume_sum_y'] != 0) & (~pd.isna(summary['Volume_sum_y']))
        summary.loc[mask, 'Volume_YoY_Change'] = \
            ((summary.loc[mask, 'Volume_sum_x'] - summary.loc[mask, 'Volume_sum_y']) / 
             summary.loc[mask, 'Volume_sum_y']) * 100
        
        summary['NSV_YoY_Change'] = 0.0
        mask = (summary['NSV_sum_y'] != 0) & (~pd.isna(summary['NSV_sum_y']))
        summary.loc[mask, 'NSV_YoY_Change'] = \
            ((summary.loc[mask, 'NSV_sum_x'] - summary.loc[mask, 'NSV_sum_y']) / 
             summary.loc[mask, 'NSV_sum_y']) * 100
        
        # Fill NaN values with 0 for previous year data
        for col in ['Volume_sum_y', 'Volume_mean_y', 'NSV_sum_y', 'NSV_mean_y']:
            if col in summary.columns:
                summary[col] = summary[col].fillna(0)
        
        # Rename columns for final display
        summary = summary.rename(columns={
            
            'Volume_sum_x': 'Current Year Total Volume',
            'Volume_mean_x': 'Current Year Avg Monthly Volume',
            'NSV_sum_x': 'Current Year Total NSV',
            'NSV_mean_x': 'Current Year Avg Monthly NSV',
            'Volume_sum_y': 'Previous Year Total Volume',
            'Volume_mean_y': 'Previous Year Avg Monthly Volume',
            'NSV_sum_y': 'Previous Year Total NSV',
            'NSV_mean_y': 'Previous Year Avg Monthly NSV',
            'Volume_YoY_Change': 'Volume YoY Change (%)',
            'NSV_YoY_Change': 'NSV YoY Change (%)'
        })
        
        # Ensure all required columns exist
        for col in ['Current Year Total Volume', 'Previous Year Total Volume']:
            if col not in summary.columns:
                summary[col] = 0
        
        # Format numbers
        summary['Current Year Total Volume'] = summary['Current Year Total Volume'].apply(lambda x: f"{float(x):,.0f}" if pd.notna(x) else '0')
        summary['Current Year Avg Monthly Volume'] = summary['Current Year Avg Monthly Volume'].apply(lambda x: f"{float(x):,.0f}" if pd.notna(x) else '0')
        summary['Current Year Total NSV'] = summary['Current Year Total NSV'].apply(lambda x: f"{float(x):,.0f} K" if pd.notna(x) else '0 K')
        summary['Current Year Avg Monthly NSV'] = summary['Current Year Avg Monthly NSV'].apply(lambda x: f"{float(x):,.0f} K" if pd.notna(x) else '0 K')
        
        summary['Previous Year Total Volume'] = summary['Previous Year Total Volume'].apply(lambda x: f"{float(x):,.0f}" if pd.notna(x) else '0')
        summary['Previous Year Avg Monthly Volume'] = summary['Previous Year Avg Monthly Volume'].apply(lambda x: f"{float(x):,.0f}" if pd.notna(x) else '0')
        summary['Previous Year Total NSV'] = summary['Previous Year Total NSV'].apply(lambda x: f"{float(x):,.0f} K" if pd.notna(x) else '0 K')
        summary['Previous Year Avg Monthly NSV'] = summary['Previous Year Avg Monthly NSV'].apply(lambda x: f"{float(x):,.0f} K" if pd.notna(x) else '0 K')
        
        # Format YoY changes
        def format_yoy(x):
            try:
                return f"{float(x):+.1f}%"
            except (ValueError, TypeError):
                return "0.0%"
                
        summary['Volume YoY Change (%)'] = summary['Volume YoY Change (%)'].apply(format_yoy)
        summary['NSV YoY Change (%)'] = summary['NSV YoY Change (%)'].apply(format_yoy)
        
        return summary
    
    # Create category summary table
    st.subheader("📋 Category Summary")
    
    category_summary = calculate_category_summary(filtered_data, is_forecast)
    
    st.dataframe(category_summary, height=400)
    
    # Create category-level time series chart
    st.subheader("📈 Category Trends")
    
    # Prepare data for category-level chart
    fig = px.area(
        category_data,
        x='Period',
        y='Volume',
        color='Category',
        title=f'Category Volume Trends - {selected_year}',
        labels={'Volume': 'Volume', 'Period': 'Month', 'Category': 'Product Category'}
    )
    
    fig.update_layout(
        xaxis_tickangle=-45,
        height=500,
        hovermode='x unified',
        legend_title_text='Category',
        showlegend=True
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # Show detailed data
    st.subheader(data_title)
    
    if filtered_data is not None and not filtered_data.empty:
        # If showing forecast, combine with actuals
        if is_forecast and 'raw_data' in st.session_state:
            # Get raw data for the same period
            raw_data = st.session_state.raw_data
            
            # Filter raw data to match the same filters as forecast
            raw_filtered = raw_data[raw_data['Period'].dt.year == selected_year]
            if selected_categories:
                raw_filtered = raw_filtered[raw_filtered['Category'].isin(selected_categories)]
            if selected_products:
                raw_filtered = raw_filtered[raw_filtered['Product'].isin(selected_products)]
            
            # Mark data sources
            filtered_data['Data_Type'] = 'Forecast'
            raw_filtered['Data_Type'] = 'Actual'
            
            # Combine actuals and forecasts
            combined_columns = ['Period', 'Product', 'Customer', 'Volume', 'NSV', 'Category', 'Data_Type']
            display_data = pd.concat([
                filtered_data[combined_columns],
                raw_filtered[combined_columns]
            ])
            
            # Sort by Period, Data_Type (Actuals first), Category, and Product
            display_data = display_data.sort_values(['Period', 'Data_Type', 'Category', 'Product'])
            
            # Pivot to show Actual and Forecast side by side
            pivot_data = display_data.melt(
                id_vars=['Period', 'Product', 'Customer', 'Category', 'Data_Type'],
                value_vars=['Volume', 'NSV']
            )
            
            pivot_data = pivot_data.pivot_table(
                index=['Period', 'Product', 'Customer', 'Category', 'variable'],
                columns='Data_Type',
                values='value',
                aggfunc='first'  # Should be only one value per group
            ).reset_index()
            
            # Calculate variance
            pivot_data['Variance'] = np.nan
            pivot_data['Variance %'] = np.nan
            
            has_actual = 'Actual' in pivot_data.columns and not pivot_data['Actual'].isna().all()
            has_forecast = 'Forecast' in pivot_data.columns and not pivot_data['Forecast'].isna().all()
            
            if has_actual and has_forecast:
                mask = ~pivot_data['Actual'].isna() & ~pivot_data['Forecast'].isna()
                pivot_data.loc[mask, 'Variance'] = pivot_data.loc[mask, 'Forecast'] - pivot_data.loc[mask, 'Actual']
                
                # Calculate percentage variance, avoiding division by zero
                non_zero_mask = (pivot_data['Actual'] != 0) & ~pivot_data['Actual'].isna()
                pivot_data.loc[non_zero_mask, 'Variance %'] = (
                    (pivot_data.loc[non_zero_mask, 'Variance'] / 
                     pivot_data.loc[non_zero_mask, 'Actual']) * 100
                ).round(1)
            
            # Format the data for display
            display_data = pivot_data.rename(columns={
                'variable': 'Metric',
                'Actual': 'Actual Value',
                'Forecast': 'Forecast Value'
            }).copy()
            
            # Ensure the Metric column exists and has a value
            if 'Metric' not in display_data.columns:
                if 'variable' in display_data.columns:
                    display_data['Metric'] = display_data['variable']
                elif 'Metric' in pivot_data.columns:
                    display_data['Metric'] = pivot_data['Metric']
                else:
                    display_data['Metric'] = ''  # Default empty value
            
            # Ensure required columns exist
            if 'Actual' not in pivot_data.columns:
                pivot_data['Actual'] = np.nan
            if 'Forecast' not in pivot_data.columns:
                pivot_data['Forecast'] = np.nan
                
            # Format numbers in the display
            def format_value(x, metric):
                if pd.isna(x):
                    return ''
                if metric == 'NSV':
                    return f"{x:,.0f} K"
                return f"{x:,.0f}"
            
            # Create formatted columns
            display_data = pivot_data.copy()
            display_data['Actual Value'] = display_data.apply(
                lambda x: format_value(x.get('Actual'), x['variable']), axis=1
            )
            display_data['Forecast Value'] = display_data.apply(
                lambda x: format_value(x.get('Forecast'), x['variable']), axis=1
            )
            
            # Select and reorder columns for display
            display_columns = ['Period', 'Product', 'Customer', 'Category']
            
            # Add Metric column if it exists
            if 'Metric' in display_data.columns:
                display_columns.append('Metric')
            
            # Add other optional columns
            optional_columns = {
                'Actual Value': 'Actual Value',
                'Forecast Value': 'Forecast Value',
                'Variance': ['Variance', 'Variance %']
            }
            
            for col, target_cols in optional_columns.items():
                if isinstance(target_cols, list):
                    for tc in target_cols:
                        if tc in display_data.columns:
                            display_columns.append(tc)
                elif col in display_data.columns:
                    display_columns.append(col)
            
            # Filter to only include columns that exist
            display_columns = [col for col in display_columns if col in display_data.columns]
            
            # Define sort columns, only including those that exist
            sort_columns = [col for col in ['Period', 'Product', 'Customer', 'Metric'] 
                          if col in display_columns]
            
            # Show the combined data
            if display_columns:
                # Create a copy to avoid SettingWithCopyWarning
                display_df = display_data[display_columns].copy()
                
                # Ensure sort columns exist in the display DataFrame
                valid_sort_columns = [col for col in sort_columns if col in display_df.columns]
                
                # Sort if we have at least one valid sort column
                if valid_sort_columns:
                    display_df = display_df.sort_values(valid_sort_columns)
                
                # Format the display
                style = display_df.style
                
                # Apply number formatting if columns exist
                if 'Variance' in display_columns:
                    style = style.format({
                        'Variance': lambda x: f"{x:,.0f}" if pd.notna(x) else ''
                    })
                if 'Variance %' in display_columns:
                    style = style.format({
                        'Variance %': lambda x: f"{x:+.1f}%" if pd.notna(x) else ''
                    })
                
                st.dataframe(style, height=600)
            else:
                st.warning("No data available to display")
            
        else:
            # Original display for non-forecast data
            columns = ['Period', 'Product', 'Customer', 'Volume', 'NSV', 'Category', 
                      'Description', 'SubCategory', 'Name', 'Region']
            
            # Limit display to last 100 rows to prevent performance issues
            display_data = filtered_data[columns].sort_values(['Period', 'Category', 'Product']).tail(100)

            st.dataframe(
                display_data
                .style.format({
                    'NSV': lambda x: f"{x:,.0f} K" if pd.notna(x) else '',
                    'Volume': lambda x: f"{x:,.0f}" if pd.notna(x) else ''
                })
            )

    # Add tabbed interface for forecast summary and downloads
    if 'forecast_data' in st.session_state and not st.session_state.forecast_data.empty:
        st.markdown("---")
        st.subheader("📊 Forecast Management")

        # Create tabs
        tab1, tab2 = st.tabs(["📈 Forecast Summary", "📥 Downloads"])

        # Tab 1: Forecast Summary (moved from sidebar)
        with tab1:
            st.write("### 📊 Forecast Summary")

            forecast_data = st.session_state.forecast_data

            # Basic forecast information
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Total Forecasts", f"{len(forecast_data):,}")

            with col2:
                st.metric("Unique Products", f"{forecast_data['Product'].nunique():,}")

            with col3:
                st.metric("Unique Customers", f"{forecast_data['Customer'].nunique():,}")

            # Forecast period and totals
            st.write("**📅 Forecast Period:**")
            st.write(f"From {forecast_data['Period'].min().strftime('%b %Y')} to {forecast_data['Period'].max().strftime('%b %Y')}")

            # Total forecast metrics
            total_forecast_volume = forecast_data['Volume'].sum()
            total_forecast_nsv = forecast_data['NSV'].sum()

            col1, col2 = st.columns(2)
            with col1:
                st.metric("Total Forecast Volume", format_number(total_forecast_volume))
            with col2:
                st.metric("Total Forecast NSV", format_currency(total_forecast_nsv))

            # Monthly breakdown
            st.write("**📈 Monthly Forecast Breakdown:**")
            monthly_forecast = forecast_data.groupby(forecast_data['Period'].dt.strftime('%Y-%m')).agg({
                'Volume': 'sum',
                'NSV': 'sum'
            }).reset_index()
            monthly_forecast.columns = ['Month', 'Volume', 'NSV']

            # Format for display
            monthly_display = monthly_forecast.copy()
            monthly_display['Volume'] = monthly_display['Volume'].apply(lambda x: f"{x:,.0f}")
            monthly_display['NSV'] = monthly_display['NSV'].apply(lambda x: f"{x:,.0f}")

            st.dataframe(monthly_display, use_container_width=True, hide_index=True)

            # Category breakdown
            if 'Category' in forecast_data.columns:
                st.write("**📊 Category Breakdown:**")
                category_breakdown = forecast_data.groupby('Category').agg({
                    'Volume': 'sum',
                    'NSV': 'sum'
                }).reset_index().sort_values('NSV', ascending=False)

                # Add percentages
                total_vol = category_breakdown['Volume'].sum()
                total_nsv = category_breakdown['NSV'].sum()
                category_breakdown['Volume %'] = (category_breakdown['Volume'] / total_vol * 100).round(1)
                category_breakdown['NSV %'] = (category_breakdown['NSV'] / total_nsv * 100).round(1)

                # Format for display
                category_display = category_breakdown.copy()
                category_display['Volume'] = category_display['Volume'].apply(lambda x: f"{x:,.0f}")
                category_display['NSV'] = category_display['NSV'].apply(lambda x: f"{x:,.0f}")
                category_display['Volume %'] = category_display['Volume %'].apply(lambda x: f"{x:.1f}%")
                category_display['NSV %'] = category_display['NSV %'].apply(lambda x: f"{x:.1f}%")

                st.dataframe(category_display, use_container_width=True, hide_index=True)

        # Tab 2: Downloads (moved from sidebar)
        with tab2:
            st.write("### 📥 Download Forecast & Historical Data")

            # Option to include historical data
            include_history = st.checkbox(
                "Include Full Historical Data",
                value=True,
                help="Include all historical data alongside forecasts for complete analysis"
            )

            # Prepare download data
            if include_history and 'raw_data' in st.session_state:
                download_data = prepare_combined_data_for_download(
                    st.session_state.forecast_data,
                    st.session_state.raw_data
                )
                data_description = "Combined Historical + Forecast Data"
            else:
                download_data = prepare_forecast_for_download(st.session_state.forecast_data)
                data_description = "Forecast Data Only"

            if not download_data.empty:
                # Show preview of download data
                st.write(f"**Download Preview ({data_description}):**")
                st.write(f"Rows: {len(download_data)}")
                st.write(f"Columns: {len(download_data.columns)}")

                # Show data breakdown if historical data is included
                if include_history and 'DataType' in download_data.columns:
                    historical_count = len(download_data[download_data['DataType'] == 'Historical'])
                    forecast_count = len(download_data[download_data['DataType'] == 'Forecast'])
                    st.write(f"- **Historical Records:** {historical_count:,}")
                    st.write(f"- **Forecast Records:** {forecast_count:,}")

                st.write("**Included columns:**")
                st.write("• " + "\n• ".join(download_data.columns))

                # File format selection
                file_format = st.selectbox(
                    "Select Format:",
                    ["Excel", "CSV"],
                    index=0,  # Excel is now default
                    help="Excel format includes category monthly splits in additional sheets"
                )

                if file_format == "Excel":
                    excel_content = "📊 **Excel file includes:**\n- Main Data Sheet"
                    if include_history:
                        excel_content += " (Historical + Forecast)"
                    else:
                        excel_content += " (Forecast Only)"
                    excel_content += "\n- Category Monthly splits\n- Category Totals\n- Monthly Totals"
                    st.info(excel_content)

                # Generate filename with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                # Prepare category splits data for Excel
                category_splits_data = None
                if file_format == "Excel":
                    forecast_data = st.session_state.forecast_data

                    # Category monthly splits
                    category_monthly = forecast_data.groupby(['Category', forecast_data['Period'].dt.strftime('%Y-%m')]).agg({
                        'Volume': 'sum',
                        'NSV': 'sum'
                    }).reset_index()
                    category_monthly.columns = ['Category', 'Month', 'Volume', 'NSV']

                    # Category totals
                    category_totals = forecast_data.groupby('Category').agg({
                        'Volume': 'sum',
                        'NSV': 'sum'
                    }).reset_index()

                    # Monthly totals
                    monthly_totals = forecast_data.groupby(forecast_data['Period'].dt.strftime('%Y-%m')).agg({
                        'Volume': 'sum',
                        'NSV': 'sum'
                    }).reset_index()
                    monthly_totals.columns = ['Month', 'Volume', 'NSV']

                    category_splits_data = {
                        'category_monthly': category_monthly,
                        'category_totals': category_totals,
                        'monthly_totals': monthly_totals
                    }

                if file_format == "CSV":
                    filename = f"sales_forecast_{timestamp}.csv"
                    file_data = create_download_link(download_data, filename, 'csv')
                    mime_type = "text/csv"
                else:
                    filename = f"sales_forecast_{timestamp}.xlsx"
                    file_data = create_download_link(download_data, filename, 'excel', category_splits_data)
                    mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

                # Download button
                if file_data:
                    st.download_button(
                        label=f"📥 Download {file_format} File",
                        data=file_data,
                        file_name=filename,
                        mime=mime_type,
                        type="primary"
                    )

                    # Show sample of data that will be downloaded
                    with st.expander("Preview Download Data"):
                        st.dataframe(download_data.head(10))
                else:
                    st.error("Error preparing download file")
            else:
                st.warning("No forecast data available for download")
