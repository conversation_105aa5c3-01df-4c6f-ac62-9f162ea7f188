SELECT
    asm.Customer,
    ac.Name AS Name,                -- Customer name
    CASE 
        WHEN ac.Area = 'S' THEN 'Southern'
        WHEN ac.Area = 'M' THEN 'Momase'
        WHEN ac.Area = 'E' THEN 'Exports'
        WHEN ac.Area = 'N' THEN 'New Guinea Islands'
        WHEN ac.Area = 'H' THEN 'Highlands'
        ELSE 'Unknown'
    END AS Region,
    asm.Warehouse,
    iwc.Description AS WHDescription,
    asm.StockCode AS Product,
    im.Description,
    LTRIM(RTRIM(
        CASE 
            WHEN CHARINDEX(' - ', SP.Description) > 0 
                THEN LEFT(SP.Description, CHARINDEX(' - ', SP.Description) - 1)
            ELSE SP.Description
        END
    )) AS Category,
    LTRIM(RTRIM(
        CASE 
            WHEN CHARINDEX(' - ', SP.Description) > 0 
                THEN SUBSTRING(SP.Description, CHARINDEX(' - ', SP.Description) + 3, LEN(SP.Description))
            ELSE ''
        END
    )) AS SubCategory,
    FORMAT(asm.TrnDate, 'yyyy-MM') AS Period,
    SUM(asm.InvoiceQty) AS Volume,
    SUM(asm.InvoiceValue) AS NSV,
    SUM(asm.InvoiceValue) + SUM(asm.DiscValue) AS GSV
FROM ArSalesMove asm
JOIN InvMaster im ON asm.StockCode = im.StockCode
JOIN ArCustomer ac ON asm.Customer = ac.Customer
JOIN SalProductClassDes SP ON im.ProductClass = SP.ProductClass
JOIN InvWhControl iwc ON asm.Warehouse = iwc.Warehouse
WHERE 
    asm.TrnDate >= DATEADD(MONTH, -24, DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()), 1))  -- start 24 months ago
    AND asm.TrnDate < DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()), 1)                   -- exclude current month
    AND asm.StockCode LIKE 'F%'      
    AND im.Description NOT LIKE '%Scrap%'
    AND asm.InvoiceQty > 0
    AND asm.InvoiceValue > 0 
GROUP BY
    asm.Customer,
    ac.Name,
    ac.Area,
    asm.Warehouse,
    iwc.Description,
    asm.StockCode,
    im.Description,
    SP.Description,
    FORMAT(asm.TrnDate, 'yyyy-MM')
ORDER BY 
    Period;